import socket
import struct
import logging
from typing import Dict, List

from entity.entitys import NetworkMetrics, ResourceAllocation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


TCP_IP = '127.0.0.1'
TCP_PORT = 10002
PATH = './ns3-fl-network'
PROGRAM = 'wifi_exp_multi_task_fl'


class NS3MultiTaskInterface:
    """Interface for NS3 Multi-Task FL simulation"""
    
    def __init__(self, config=None, host=TCP_IP, port=TCP_PORT):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.num_clients = config.clients.total if config else 5
        self.network_type = config.network.type if config else 'wifi'
        
    def connect(self) -> bool:
        """Connect to NS3 simulation"""
        command = './waf --run "' + PROGRAM + ' --NumClients=' + str(self.num_clients) + ' --NetworkType=' + self.network_type + '"'
        print(command)
        while True:
            user_input = input("是否成功运行了ns3? 1表示成功, 其他输入表示失败：")
            print('')  # 换行
            if user_input in ['1']:
                break
            else:
                print("请启动ns3")

        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.connected = True
            logger.info(f"Connected to NS3 simulation at {self.host}:{self.port}")
            return True
        except Exception as e:
            self.connected = False
            logger.error(f"Failed to connect to NS3: {e}")
            return False
    
    def close(self):
        """Close connection to NS3"""
        if self.socket:
            try:
                # Send exit command
                header = struct.pack('III', 2, 0, 0)  # EXIT command
                self.socket.send(header)
            except:
                pass
            self.socket.close()
            self.socket = None
            self.connected = False
            logger.info("Disconnected from NS3 simulation")
    
    def _calculate_compute_time_T_ij(self, q_ij: float, task_data_size: float = 1000.0, 
                                   resource_per_sample: float = 1.0) -> float:
        """
        Calculate compute time T_ij from q_ij
        Formula: T_ij = (task_data_size * resource_per_sample) / (q_ij * max_compute_resource)
        """
        max_compute_resource = 100.0  # Maximum compute resource units
        min_resource = 0.01          # Minimum resource to avoid division by zero
        
        # Ensure q_ij is in valid range
        q_ij = max(min_resource, min(1.0, q_ij))
        
        # Calculate allocated compute resource
        allocated_resource = q_ij * max_compute_resource
        
        # Calculate compute time
        compute_time = (task_data_size * resource_per_sample) / allocated_resource
        
        # Clamp to reasonable bounds (0.5-20 seconds)
        compute_time = max(0.5, min(20.0, compute_time))
        
        return compute_time

    def _calculate_file_size(self, task_id: int, client_id: int) -> float:
        """
        Calculate file size Fs_ij based on task type and client characteristics

        Args:
            task_id: Task ID
            client_id: Client ID

        Returns:
            File size in MB
        """
        base_file_size = 10.0  # Base file size in MB

        # Different tasks have different file sizes
        task_size_factor = 1.0
        if task_id == 0:  # MNIST-like task
            task_size_factor = 0.5
        elif task_id == 1:  # CIFAR10-like task
            task_size_factor = 1.0
        elif task_id == 2:  # Air Quality-like task
            task_size_factor = 0.3
        else:
            task_size_factor = 0.8

        # Client factor (some clients may have larger models)
        client_factor = 1.0 + (client_id * 0.1)

        file_size = base_file_size * task_size_factor * client_factor

        # Clamp to reasonable bounds (1-50 MB)
        file_size = max(1.0, min(50.0, file_size))

        return file_size

    
    def send_simulation_command(self, client_allocations: Dict[int, ResourceAllocation], 
                                num_clients: int, num_tasks: int):
        """Send simulation command to NS3"""
        
        # Send command header
        command = 1  # RUN_SIMULATION
        header = struct.pack('III', command, num_clients, num_tasks)
        self.socket.send(header)
        
        logger.debug(f"Sent command header: command={command}, clients={num_clients}, tasks={num_tasks}")
        
        # Send data for each client
        for client_id in range(num_clients):
            # Send client ID
            self.socket.send(struct.pack('I', client_id))
            
            # Get allocation for this client
            allocation = client_allocations.get(client_id)
            if not allocation:
                # Send default values if no allocation (6 values per task now)
                for _ in range(num_tasks * 6):
                    self.socket.send(struct.pack('d', 0.5))
                continue
            
            # Send resource allocation data: [select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij] * num_tasks
            is_info = False
            for task_id in range(num_tasks):
                task_name = f"task_{task_id}"

                # Extract values (default to 0.5 if not present)
                select_ij = allocation.select_ij.get(task_name, 0.5)
                q_ij = allocation.q_ij.get(task_name, 0.5)
                B_ij = allocation.B_ij.get(task_name, 0.5)
                P_ij = allocation.P_ij.get(task_name, 0.5)
                Task_ij = allocation.Task_ij.get(task_name, 0.5)

                # Calculate T_ij from q_ij (only once, here in Python)
                T_ij = self._calculate_compute_time_T_ij(q_ij) if select_ij >= 0.5 else 0.0

                # Calculate file size Fs_ij based on task type
                Fs_ij = self._calculate_file_size(task_id, client_id) if select_ij >= 0.5 else 0.0

                # Send 6 values per task: [select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij]
                self.socket.send(struct.pack('d', select_ij))
                self.socket.send(struct.pack('d', T_ij))        # Send T_ij instead of q_ij
                self.socket.send(struct.pack('d', B_ij))
                self.socket.send(struct.pack('d', P_ij))
                self.socket.send(struct.pack('d', Task_ij))
                self.socket.send(struct.pack('d', Fs_ij))       # Send Fs_ij (file size)

                # Log task info
                if select_ij >= 0.5:  # Task will be executed 
                    is_info=True
                    logger.info(f"Client {client_id} {task_name}: "
                               f"select={select_ij:.2f}, q_ij={q_ij:.2f} → T_ij={T_ij:.2f}s, "
                               f"B_ij={B_ij:.2f}, P_ij={P_ij:.2f}, Task_ij={Task_ij:.2f}, "
                               f"Fs_ij={Fs_ij:.2f}MB")
            if is_info:
                logger.info(f"Sent allocation data for client {client_id}")
    
    def receive_results(self) -> List[NetworkMetrics]:
        """Receive simulation results from NS3"""
        
        # Receive number of results
        data = self.socket.recv(4)
        if len(data) != 4:
            raise Exception("Failed to receive number of results")
        
        num_results = struct.unpack('I', data)[0]
        logger.debug(f"Expecting {num_results} task results")
        
        results = []
        
        for i in range(num_results):
            logger.info(f"Receiving result {i+1} from ns3")
            # Receive client ID and task ID
            data = self.socket.recv(8)  # 2 * uint32_t
            client_id, task_id = struct.unpack('II', data)
            
            # Receive task name
            data = self.socket.recv(4)
            name_len = struct.unpack('I', data)[0]
            data = self.socket.recv(name_len)
            task_name = data.decode('utf-8')
            
            # Receive metrics: bandwidth, power, packetLoss, throughput, latency, executionTime
            data = self.socket.recv(8 * 6)  # 6 double values
            metrics = struct.unpack('dddddd', data)
            
            bandwidth, power, packet_loss, throughput, latency, execution_time = metrics
            
            result = NetworkMetrics(
                client_id=client_id,
                task_id=task_id,
                task_name=task_name,
                bandwidth=bandwidth,
                power=power,
                packet_loss=packet_loss,
                throughput=throughput,
                latency=latency,
                execution_time=execution_time
            )
            
            results.append(result)
        
        return results