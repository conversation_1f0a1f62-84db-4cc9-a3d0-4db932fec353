from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any


class TaskType(Enum):
    """Task types"""
    IMAGE_CLASSIFICATION = "image"
    TIME_SERIES = "time_series"


@dataclass
class TaskConfig:
    """Configuration for a single task"""
    name: str
    task_type: TaskType
    data_path: str
    model_type: str
    batch_size: int = 32
    learning_rate: float = 0.001
    local_epochs: int = 5
    priority: float = 1.0


@dataclass
class MAPPOConfig:
    """Configuration for MAPPO algorithm"""
    state_dim: int = 20
    action_dim: int = 10
    hidden_dim: int = 256
    learning_rate: float = 3e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    ppo_epochs: int = 4
    batch_size: int = 64
    buffer_size: int = 2048

@dataclass
class MultiTaskFLConfig:
    """Configuration for multi-task federated learning"""
    num_clients: int = 5
    num_rounds: int = 100
    clients_per_round: int = 3
    similarity_threshold: float = 0.5

    # MAPPO configuration
    mappo_config: MAPPOConfig = None

    # Task configurations
    tasks: Dict[str, TaskConfig] = None

    # Network configuration
    network_config: Any = None

@dataclass
class ResourceAllocation:
    """Resource allocation from MAPPO"""
    # Client selection: select_ij - whether client i participates in task j
    select_ij: Dict[str, float]  # Task -> Selection probability [0,1]

    # Computing resource allocation: q_ij - computing resources allocated by client i for task j
    q_ij: Dict[str, float]  # Task -> Computing resource allocation [0,1]

    # Bandwidth allocation: B_ij - bandwidth allocated by client i for task j
    B_ij: Dict[str, float]  # Task -> Bandwidth allocation [0,1]

    # Power allocation: P_ij - power allocated by client i for task j
    P_ij: Dict[str, float]  # Task -> Power allocation [0,1]

    # Task priority: Task_ij - task priority allocated by client i for task j
    Task_ij: Dict[str, float]  # Task -> Task priority [0,1]


@dataclass
class NetworkMetrics:
    """Task simulation result"""
    client_id: int
    task_id: int
    task_name: str
    bandwidth: float        # Actual bandwidth used (MHz)
    power: float           # Actual power used (dBm)
    packet_loss: float     # Packet loss ratio
    throughput: float      # Achieved throughput (Mbps)
    latency: float         # Average latency (ms)
    execution_time: float  # Actual execution time (s)

