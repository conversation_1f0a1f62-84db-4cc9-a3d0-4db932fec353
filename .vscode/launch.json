{"version": "0.2.0", "configurations": [{"name": "waf (gdb) Launch from scratch", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/ns3-fl-network/build/scratch/wifi_exp_multi_task_fl/wifi_exp_multi_task_fl", "args": ["--NumClients=5", "--NumTasks=3"], "stopAtEntry": false, "cwd": "${workspaceFolder}/ns3-fl-network", "preLaunchTask": "ns-build", "environment": [{"name": "LD_LIBRARY_PATH", "value": "${workspaceFolder}/ns3-fl-network/build/lib"}], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/local/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "logging": {"engineLogging": true, "trace": true, "traceResponse": true}}]}