{
	"version": "2.0.0",
	"tasks": [
		{
			"type": "cppbuild",
			"label": "ns-build",
			"command": "./waf",
			"args": [
				// "-fdiagnostics-color=always",
				// "-g",
				// "${file}",
				// "-o",
				// "${fileDirname}/${fileBasenameNoExtension}"
			],
			"options": {
				"cwd": "${workspaceFolder}"
			},
			"problemMatcher": [
				"$gcc"
			],
			"group": {
				"kind": "build",
				"isDefault": true
			},
			"detail": "compiler: /usr/bin/cpp"
		}
	]
}