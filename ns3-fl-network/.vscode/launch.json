{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "(gdb) Launch from scratch",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/scratch/wifi_exp_multi_task_fl/wifi_exp_multi_task_fl",  // 可执行文件路径
            "args": ["--NumClients=5", "--NumTasks=3"],  // 传递给程序的命令行参数
            "stopAtEntry": false,  // 是否在入口处停止
            "cwd": "${workspaceFolder}",  // 当前工作目录
            "preLaunchTask": "ns-build",  // 在调试前构建项目
            "environment": [
                {
                    "name": "LD_LIBRARY_PATH",
                    "value": "${workspaceFolder}/build/lib/"
                }
            ],
            "externalConsole": true,  // 使用外部控制台输出
            "MIMode": "gdb",  // 使用 gdb 调试器
            "miDebuggerPath": "/usr/bin/gdb",  // gdb 路径
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ]
        }
    ]
}
