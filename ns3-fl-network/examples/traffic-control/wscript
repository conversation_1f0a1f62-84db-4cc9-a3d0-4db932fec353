## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('traffic-control',
                                 ['internet', 'point-to-point', 'applications', 'traffic-control', 'flow-monitor'])
    obj.source = 'traffic-control.cc'

    obj = bld.create_ns3_program('queue-discs-benchmark',
                                 ['internet', 'point-to-point', 'applications', 'internet-apps', 'traffic-control', 'flow-monitor'])
    obj.source = 'queue-discs-benchmark.cc'

    obj = bld.create_ns3_program('red-vs-fengadaptive', ['point-to-point', 'point-to-point-layout', 'internet', 'applications', 'traffic-control'])
    obj.source = 'red-vs-fengadaptive.cc'

    obj = bld.create_ns3_program('red-vs-nlred', ['point-to-point', 'point-to-point-layout', 'internet', 'applications', 'traffic-control'])
    obj.source = 'red-vs-nlred.cc'

    obj = bld.create_ns3_program('tbf-example',
                                 ['internet', 'point-to-point', 'applications', 'traffic-control'])
    obj.source = 'tbf-example.cc'

    obj = bld.create_ns3_program('cobalt-vs-codel',
                                 ['internet', 'point-to-point', 'applications', 'traffic-control'])
    obj.source = 'cobalt-vs-codel.cc'

