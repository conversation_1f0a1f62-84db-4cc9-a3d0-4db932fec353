## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('socket-bound-static-routing', ['network', 'csma', 'point-to-point', 'internet'])
    obj.source = 'socket-bound-static-routing.cc'

    obj = bld.create_ns3_program('socket-bound-tcp-static-routing', ['network', 'csma', 'point-to-point', 'internet', 'applications'])
    obj.source = 'socket-bound-tcp-static-routing.cc'

    obj = bld.create_ns3_program('socket-options-ipv4', ['network', 'csma', 'point-to-point', 'internet'])
    obj.source = 'socket-options-ipv4.cc'

    obj = bld.create_ns3_program('socket-options-ipv6', ['network', 'csma', 'point-to-point', 'internet'])
    obj.source = 'socket-options-ipv6.cc'
