## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('mixed-wired-wireless', ['wifi', 'applications', 'olsr', 'netanim'])
    obj.source = 'mixed-wired-wireless.cc'

    bld.register_ns3_script('mixed-wired-wireless.py', ['wifi', 'applications', 'olsr'])

    obj = bld.create_ns3_program('wifi-adhoc', ['wifi', 'applications'])
    obj.source = 'wifi-adhoc.cc'

    obj = bld.create_ns3_program('wifi-clear-channel-cmu', ['internet', 'wifi'])
    obj.source = 'wifi-clear-channel-cmu.cc'

    obj = bld.create_ns3_program('wifi-ap', ['wifi', 'applications'])
    obj.source = 'wifi-ap.cc'

    bld.register_ns3_script('wifi-ap.py', ['wifi', 'applications'])

    obj = bld.create_ns3_program('wifi-wired-bridging', ['wifi', 'csma', 'bridge', 'applications'])
    obj.source = 'wifi-wired-bridging.cc'

    obj = bld.create_ns3_program('wifi-multirate', ['wifi', 'flow-monitor', 'olsr', 'applications'])
    obj.source = 'wifi-multirate.cc'

    obj = bld.create_ns3_program('wifi-simple-adhoc', ['internet', 'wifi'])
    obj.source = 'wifi-simple-adhoc.cc'

    obj = bld.create_ns3_program('wifi-simple-adhoc-grid', ['internet', 'wifi', 'olsr'])
    obj.source = 'wifi-simple-adhoc-grid.cc'

    obj = bld.create_ns3_program('wifi-simple-infra', ['internet', 'wifi'])
    obj.source = 'wifi-simple-infra.cc'

    obj = bld.create_ns3_program('wifi-simple-interference', ['internet', 'wifi'])
    obj.source = 'wifi-simple-interference.cc'

    obj = bld.create_ns3_program('wifi-blockack', ['wifi', 'applications'])
    obj.source = 'wifi-blockack.cc'

    obj = bld.create_ns3_program('wifi-dsss-validation', ['wifi'])
    obj.source = 'wifi-dsss-validation.cc'

    obj = bld.create_ns3_program('wifi-ofdm-validation', ['wifi'])
    obj.source = 'wifi-ofdm-validation.cc'

    obj = bld.create_ns3_program('wifi-ofdm-ht-validation', ['wifi'])
    obj.source = 'wifi-ofdm-ht-validation.cc'

    obj = bld.create_ns3_program('wifi-ofdm-vht-validation', ['wifi'])
    obj.source = 'wifi-ofdm-vht-validation.cc'

    obj = bld.create_ns3_program('wifi-hidden-terminal', ['wifi', 'applications', 'flow-monitor'])
    obj.source = 'wifi-hidden-terminal.cc'

    obj = bld.create_ns3_program('wifi-ht-network', ['wifi', 'applications'])
    obj.source = 'wifi-ht-network.cc'

    obj = bld.create_ns3_program('wifi-vht-network', ['wifi', 'applications'])
    obj.source = 'wifi-vht-network.cc'

    obj = bld.create_ns3_program('wifi-timing-attributes', ['wifi', 'applications'])
    obj.source = 'wifi-timing-attributes.cc'

    obj = bld.create_ns3_program('wifi-sleep', ['wifi', 'applications'])
    obj.source = 'wifi-sleep.cc'
    
    obj = bld.create_ns3_program('wifi-power-adaptation-distance', ['wifi', 'applications'])
    obj.source = 'wifi-power-adaptation-distance.cc'
    
    obj = bld.create_ns3_program('wifi-power-adaptation-interference', ['wifi', 'applications', 'flow-monitor'])
    obj.source = 'wifi-power-adaptation-interference.cc'

    obj = bld.create_ns3_program('wifi-rate-adaptation-distance', ['wifi', 'applications'])
    obj.source = 'wifi-rate-adaptation-distance.cc'

    obj = bld.create_ns3_program('wifi-aggregation', ['wifi', 'applications'])
    obj.source = 'wifi-aggregation.cc'

    obj = bld.create_ns3_program('wifi-txop-aggregation', ['wifi', 'applications'])
    obj.source = 'wifi-txop-aggregation.cc'

    obj = bld.create_ns3_program('wifi-simple-ht-hidden-stations', ['wifi', 'applications'])
    obj.source = 'wifi-simple-ht-hidden-stations.cc'

    obj = bld.create_ns3_program('wifi-80211n-mimo', ['wifi', 'applications'])
    obj.source = 'wifi-80211n-mimo.cc'

    obj = bld.create_ns3_program('wifi-mixed-network', ['wifi', 'applications'])
    obj.source = 'wifi-mixed-network.cc'

    obj = bld.create_ns3_program('wifi-tcp', ['wifi', 'applications'])    
    obj.source = 'wifi-tcp.cc'

    obj = bld.create_ns3_program('wifi-80211e-txop', ['wifi', 'applications'])
    obj.source = 'wifi-80211e-txop.cc'

    obj = bld.create_ns3_program('wifi-spectrum-per-example', ['wifi', 'applications'])
    obj.source = 'wifi-spectrum-per-example.cc'

    obj = bld.create_ns3_program('wifi-spectrum-per-interference', ['wifi', 'applications'])
    obj.source = 'wifi-spectrum-per-interference.cc'

    obj = bld.create_ns3_program('wifi-spectrum-saturation-example', ['wifi', 'applications'])
    obj.source = 'wifi-spectrum-saturation-example.cc'

    obj = bld.create_ns3_program('wifi-ofdm-he-validation', ['wifi'])
    obj.source = 'wifi-ofdm-he-validation.cc'

    obj = bld.create_ns3_program('wifi-he-network', ['wifi', 'applications'])
    obj.source = 'wifi-he-network.cc'

    obj = bld.create_ns3_program('wifi-multi-tos', ['wifi', 'applications'])
    obj.source = 'wifi-multi-tos.cc'

    obj = bld.create_ns3_program('wifi-backward-compatibility', ['wifi', 'applications'])
    obj.source = 'wifi-backward-compatibility.cc'

    obj = bld.create_ns3_program('wifi-spatial-reuse', ['wifi', 'applications'])
    obj.source = 'wifi-spatial-reuse.cc'

    obj = bld.create_ns3_program('wifi-error-models-comparison', ['wifi'])
    obj.source = 'wifi-error-models-comparison.cc'
