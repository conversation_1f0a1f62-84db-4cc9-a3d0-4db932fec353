## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('dynamic-global-routing',
                                 ['point-to-point', 'csma', 'internet', 'applications'])
    obj.source = 'dynamic-global-routing.cc'

    obj = bld.create_ns3_program('static-routing-slash32',
                                 ['point-to-point', 'csma', 'internet', 'applications'])
    obj.source = 'static-routing-slash32.cc'

    obj = bld.create_ns3_program('global-routing-slash32',
                                 ['point-to-point', 'csma', 'internet', 'applications'])
    obj.source = 'global-routing-slash32.cc'

    obj = bld.create_ns3_program('global-injection-slash32',
                                 ['point-to-point', 'csma', 'internet', 'applications'])
    obj.source = 'global-injection-slash32.cc'

    obj = bld.create_ns3_program('simple-global-routing',
                                 ['point-to-point', 'internet', 'applications', 'flow-monitor'])
    obj.source = 'simple-global-routing.cc'

    obj = bld.create_ns3_program('simple-alternate-routing',
                                 ['point-to-point', 'internet', 'applications'])
    obj.source = 'simple-alternate-routing.cc'

    obj = bld.create_ns3_program('mixed-global-routing',
                                 ['point-to-point', 'internet', 'csma', 'applications'])
    obj.source = 'mixed-global-routing.cc'

    obj = bld.create_ns3_program('simple-routing-ping6',
                                 ['csma', 'internet', 'internet-apps'])
    obj.source = 'simple-routing-ping6.cc'

    obj = bld.create_ns3_program('manet-routing-compare',
                                 ['wifi', 'dsr', 'dsdv', 'aodv', 'olsr', 'internet', 'applications'])
    obj.source = 'manet-routing-compare.cc'

    obj = bld.create_ns3_program('ripng-simple-network',
                                 ['csma', 'internet', 'internet-apps'])
    obj.source = 'ripng-simple-network.cc'

    bld.register_ns3_script('simple-routing-ping6.py', ['csma', 'internet', 'internet-apps'])

    obj = bld.create_ns3_program('rip-simple-network',
                                 ['csma', 'internet', 'internet-apps'])
    obj.source = 'rip-simple-network.cc'

    obj = bld.create_ns3_program('global-routing-multi-switch-plus-router',
                                 ['core', 'network', 'applications', 'internet', 'bridge', 'csma', 'point-to-point', 'csma', 'internet'])
    obj.source = 'global-routing-multi-switch-plus-router.cc'

    obj = bld.create_ns3_program('simple-multicast-flooding',
                                 ['core', 'network', 'applications', 'internet'])
    obj.source = 'simple-multicast-flooding.cc'
