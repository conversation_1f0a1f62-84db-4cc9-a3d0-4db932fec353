## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('hello-simulator', ['core'])
    obj.source = 'hello-simulator.cc'
        
    obj = bld.create_ns3_program('first', ['core', 'point-to-point', 'internet', 'applications'])
    obj.source = 'first.cc'

    bld.register_ns3_script('first.py', ['internet', 'point-to-point', 'applications'])
        
    obj = bld.create_ns3_program('second', ['core', 'point-to-point', 'csma', 'internet', 'applications'])
    obj.source = 'second.cc'
        
    bld.register_ns3_script('second.py', ['core', 'point-to-point', 'csma', 'internet', 'applications'])

    obj = bld.create_ns3_program('third', ['core', 'point-to-point', 'csma', 'wifi', 'internet', 'applications'])
    obj.source = 'third.cc'

    bld.register_ns3_script('third.py', ['core', 'point-to-point', 'csma', 'wifi', 'internet', 'applications'])

    obj = bld.create_ns3_program('fourth', ['core'])
    obj.source = 'fourth.cc'

    obj = bld.create_ns3_program('fifth', ['core', 'point-to-point', 'internet', 'applications'])
    obj.source = 'fifth.cc'

    obj = bld.create_ns3_program('sixth', ['core', 'point-to-point', 'internet', 'applications'])
    obj.source = 'sixth.cc'

    obj = bld.create_ns3_program('seventh', ['core', 'stats', 'point-to-point', 'internet', 'applications'])
    obj.source = 'seventh.cc'
