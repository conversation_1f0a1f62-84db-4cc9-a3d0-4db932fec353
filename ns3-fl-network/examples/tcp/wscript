## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('tcp-large-transfer',
                                 ['point-to-point', 'applications', 'internet'])
    obj.source = 'tcp-large-transfer.cc'

    obj = bld.create_ns3_program('tcp-nsc-lfn',
                                 ['point-to-point', 'applications', 'internet'])
    obj.source = 'tcp-nsc-lfn.cc'

    obj = bld.create_ns3_program('tcp-nsc-zoo',
                                 ['csma', 'internet', 'applications'])
    obj.source = 'tcp-nsc-zoo.cc'

    obj = bld.create_ns3_program('tcp-star-server',
                                 ['point-to-point', 'applications', 'internet'])
    obj.source = 'tcp-star-server.cc'

    obj = bld.create_ns3_program('star',
                                 ['netanim', 'point-to-point', 'point-to-point-layout', 'applications', 'internet'])
    obj.source = 'star.cc'

    obj = bld.create_ns3_program('tcp-bulk-send',
                                 ['point-to-point', 'applications', 'internet'])
    obj.source = 'tcp-bulk-send.cc'

    obj = bld.create_ns3_program('tcp-pcap-nanosec-example',
                                 ['point-to-point', 'applications', 'internet'])
    obj.source = 'tcp-pcap-nanosec-example.cc'

    obj = bld.create_ns3_program('tcp-nsc-comparison',
                                 ['point-to-point', 'internet', 'applications', 'flow-monitor'])

    obj.source = 'tcp-nsc-comparison.cc'

    obj = bld.create_ns3_program('tcp-variants-comparison',
                                 ['point-to-point', 'internet', 'applications', 'flow-monitor'])

    obj.source = 'tcp-variants-comparison.cc'

    obj = bld.create_ns3_program('tcp-pacing',
                                 ['point-to-point', 'internet', 'applications', 'flow-monitor'])

    obj.source = 'tcp-pacing.cc'

    obj = bld.create_ns3_program('dctcp-example',
                                 ['core', 'network', 'internet', 'point-to-point', 'applications', 'traffic-control'])
    obj.source = 'dctcp-example.cc'

    obj = bld.create_ns3_program('tcp-linux-reno',
                                 ['point-to-point', 'internet', 'applications', 'traffic-control', 'network'])

    obj.source = 'tcp-linux-reno.cc'

    obj = bld.create_ns3_program('tcp-validation',
                                 ['point-to-point', 'internet', 'applications', 'traffic-control', 'network', 'internet-apps'])

    obj.source = 'tcp-validation.cc'

    obj = bld.create_ns3_program('tcp-bbr-example',
                                 ['point-to-point', 'internet', 'applications', 'traffic-control', 'network', 'internet-apps', 'flow-monitor'])

    obj.source = 'tcp-bbr-example.cc'
