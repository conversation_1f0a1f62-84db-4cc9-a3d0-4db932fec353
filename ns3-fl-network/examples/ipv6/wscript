## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('icmpv6-redirect', ['csma', 'internet', 'internet-apps'])
    obj.source = 'icmpv6-redirect.cc'

    obj = bld.create_ns3_program('ping6', ['csma', 'internet', 'internet-apps'])
    obj.source = 'ping6.cc'
    
    obj = bld.create_ns3_program('radvd', ['csma', 'internet', 'internet-apps'])
    obj.source = 'radvd.cc'

    obj = bld.create_ns3_program('radvd-two-prefix', ['csma', 'internet', 'internet-apps'])
    obj.source = 'radvd-two-prefix.cc'

    obj = bld.create_ns3_program('test-ipv6', ['point-to-point', 'internet'])
    obj.source = 'test-ipv6.cc'
    
    obj = bld.create_ns3_program('fragmentation-ipv6', ['csma', 'internet', 'internet-apps'])
    obj.source = 'fragmentation-ipv6.cc'
    
    obj = bld.create_ns3_program('fragmentation-ipv6-two-MTU', ['csma', 'internet', 'internet-apps'])
    obj.source = 'fragmentation-ipv6-two-MTU.cc'

    obj = bld.create_ns3_program('loose-routing-ipv6', ['csma', 'internet', 'internet-apps'])
    obj.source = 'loose-routing-ipv6.cc'

    obj = bld.create_ns3_program('wsn-ping6', ['lr-wpan', 'internet', 'sixlowpan', 'mobility', 'internet-apps'])
    obj.source = 'wsn-ping6.cc'

