# NS3 Multi-Task Federated Learning Simulation

This module implements a complete NS3 simulation for multi-task federated learning with dynamic resource allocation based on MAPPO decisions.

## 🎯 Overview

The system receives `ResourceAllocation` data from Python MAPPO and performs realistic network simulation with sequential task execution based on priority ordering.

## 📊 Core Workflow

### Step 1: Resource Allocation Processing
```python
class ResourceAllocation:
    select_ij: Dict[str, float]  # Task selection [0,1] → >=0.5 becomes 1, else 0
    q_ij: Dict[str, float]       # Computing resources [0,1] → T_ij calculation
    B_ij: Dict[str, float]       # Bandwidth allocation [0,1] → Channel width
    P_ij: Dict[str, float]       # Power allocation [0,1] → TX power
    Task_ij: Dict[str, float]    # Task priority [0,1] → Execution order
```

### Step 2: Compute Time Calculation (Python Side)
```python
# Formula: T_ij = (taskDataSize * resourcePerSample) / (q_ij * maxComputeResource)
T_ij = calculate_compute_time_T_ij(q_ij, taskDataSize=1000.0, resourcePerSample=1.0)
# T_ij is calculated once in Python and sent directly to NS3
```

### Step 3: Data Transmission
- **Python → NS3**: `[select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij]` per task
- **No Redundant Calculation**: T_ij and Fs_ij computed once in Python, not recalculated in NS3

### Step 4: Task Execution Plan
- **Selection**: Only tasks with `select_ij >= 0.5` are executed
- **Priority Ordering**: Tasks sorted by `Task_ij` (highest first) within each client
- **Sequential Execution**: Each task has two phases:
  1. **Compute Phase**: Device computes for T_ij seconds
  2. **WiFi Phase**: Device transmits data via WiFi after computation

### Step 4: Network Configuration
- **Channel Width**: `B_ij` → 10/20/40/80 MHz based on bandwidth allocation
- **TX Power**: `P_ij` → 5-30 dBm based on power allocation  
- **File Size**: `Fs_ij` calculated based on task type and client characteristics

### Step 5: Simulation & Results
- **Compute Phase**: Device computes for T_ij seconds (no network activity)
- **WiFi Phase**: Device transmits Fs_ij data with configured B_ij and P_ij
- **Transmission Time**: Total time to send complete file (used for task scheduling)
- **Latency**: Average per-packet delay (network performance metric)
- Results sent back to Python: `client_id, bandwidth, power, packet_loss, throughput, latency`

## 🔧 Key Features

### 1. **Direct Resource Mapping**
```cpp
// Bandwidth allocation to channel width
B_ij >= 0.8 → 80 MHz
B_ij >= 0.6 → 40 MHz  
B_ij >= 0.4 → 20 MHz
B_ij <  0.4 → 10 MHz

// Power allocation to transmission power
P_ij → 5 + (P_ij * 25) dBm  // Range: 5-30 dBm
```

### 2. **Task Priority Execution**
- Tasks within each client sorted by `Task_ij` (descending)
- Sequential execution with proper start times
- File transfer applications created for each task

### 3. **Realistic File Transfer**
- File size `Fs_ij` based on task type (MNIST: 5MB, CIFAR10: 10MB, etc.)
- TCP bulk send applications for realistic network traffic
- Packet size adapted based on bandwidth allocation

## 📡 Socket Protocol

### Command Format
```cpp
struct Header {
    uint32_t command;      // 1=RUN_SIMULATION, 2=EXIT
    uint32_t numClients;   // Number of clients
    uint32_t numTasks;     // Number of tasks per client
};
```

### Resource Data (per client)
```cpp
uint32_t clientId;
// For each task: [select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij]
double allocations[numTasks * 6];
```

### Results Format
```cpp
struct TaskResult {
    uint32_t clientId;
    uint32_t taskId;
    string taskName;
    double bandwidth;      // Actual channel width (MHz)
    double power;         // Actual TX power (dBm)
    double packetLoss;    // Packet loss ratio [0,1]
    double throughput;    // Achieved throughput (Mbps)
    double latency;       // Average latency (ms)
    double executionTime; // Actual execution time (s)
};
```

## 🚀 Usage

### 1. Build NS3 Project
```bash
cd ns3-fl-network
./waf configure
./waf build
```

### 2. Run NS3 Simulation
```bash
./waf --run "wifi_exp_multi_task_fl --NumClients=3 --NumTasks=3 --NetworkType=wifi"
```

### 3. Run Python Test
```bash
python3 test_ns3_multi_task_complete.py
```

## 📈 Example Execution Flow

### Input (Python → NS3)
```
Client 0:
  task_0: select=1.0, T_ij=2.5s (from q_ij=0.8), B_ij=0.7, P_ij=0.6, Task_ij=0.9, Fs_ij=5.0MB
  task_1: select=1.0, T_ij=4.0s (from q_ij=0.5), B_ij=0.5, P_ij=0.4, Task_ij=0.7, Fs_ij=10.0MB
  task_2: select=0.3 → Not executed

Execution Timeline (by Task_ij):
  1. task_0 (priority=0.9):
     - Start=1.0s, Compute=2.5s (T_ij), WiFi=1.2s, Total=3.7s
     - WiFi transmission starts at 3.5s
  2. task_1 (priority=0.7):
     - Start=4.2s, Compute=4.0s (T_ij), WiFi=2.0s, Total=6.0s
     - WiFi transmission starts at 8.2s
```

### NS3 Configuration
```
task_0: ChannelWidth=40MHz, TxPower=20dBm, FileSize=10MB
task_1: ChannelWidth=20MHz, TxPower=15dBm, FileSize=10MB
```

### Output (NS3 → Python)
```
Client 0 task_0: bandwidth=40.0MHz, power=20.0dBm, throughput=25.3Mbps, latency=12.5ms, packet_loss=0.001
Client 0 task_1: bandwidth=20.0MHz, power=15.0dBm, throughput=18.7Mbps, latency=18.2ms, packet_loss=0.003
```

## 🔍 Performance Correlations

- **Higher q_ij** → Lower T_ij → Faster task execution
- **Higher B_ij** → Wider channel → Better throughput
- **Higher P_ij** → Higher TX power → Lower packet loss
- **Higher Task_ij** → Earlier execution → Priority scheduling

## 📊 Metrics Explanation

### Transmission Time vs Latency
- **Transmission Time**: Total time to send complete file (Fs_ij ÷ data_rate)
  - Used for: Task scheduling, determining when next task can start
  - Example: 10MB file at 20Mbps = 4 seconds transmission time

- **Latency**: Average delay per packet (from FlowMonitor)
  - Used for: Network performance evaluation, QoS assessment
  - Example: Average packet delay = 15ms

- **Relationship**: A file transmission may take 4 seconds (transmission time) but each packet has 15ms latency

## 📝 Configuration Parameters

```bash
--NumClients=5        # Number of clients
--NumTasks=3          # Number of tasks per client  
--NetworkType=wifi    # Network type (wifi/ethernet)
--TxGain=5.0         # Additional TX gain (dB)
--MaxPacketSize=1024  # Maximum packet size (bytes)
```

## 📊 Output Files

- **CSV Results**: `multi_task_sync_wifi_5.0_YYYY-MM-DD_HH-MM-SS.csv`
- **NS3 Logs**: Console output with detailed execution information

## 🔧 Customization

### Adding New Task Types
```cpp
// In CalculateFileSize()
case 3: // New task type
    taskSizeFactor = 1.5;  // Custom file size factor
    break;
```

### Modifying Resource Mapping
```cpp
// In ConfigureChannelWidth()
if (effectiveBandwidth >= 0.9) {
    return 160;  // Support 160 MHz channels
}
```

This implementation provides a complete, realistic simulation of multi-task federated learning with proper resource allocation, priority-based execution, and detailed performance metrics.
