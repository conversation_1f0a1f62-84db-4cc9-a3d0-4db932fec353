/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-fl-experiment.h"
#include "multi-task-fl-socket-interface.h"
#include <random>
#include <chrono>
#include <cmath>

using sysclock_t = std::chrono::system_clock;

using namespace ns3;

// Global variables
MultiTaskFLSocketInterface g_socketInterface (10002);
std::map<int, std::shared_ptr<MultiTaskClientNode>> g_clientNodes;

NS_LOG_COMPONENT_DEFINE ("MultiTaskFL");

int
main (int argc, char *argv[])
{

  // Multi-task FL parameters
  std::string dataRate = "250kbps"; /* Application layer datarate. */
  int numClients = 5; /* Number of clients */
  int numTasks = 3; /* Number of tasks per client */
  std::string NetworkType = "wifi"; /* Network type */
  int MaxPacketSize = 1024; /* Max packet size in bytes */
  double TxGain = 0.0; /* TX gain in dB */
  double ModelSize = 1.500 * 10; /* Model size in kb */
  std::string learningModel = "sync"; /* Learning model type */

  CommandLine cmd (__FILE__);

  cmd.AddValue ("NumClients", "Number of clients", numClients);
  cmd.AddValue ("NumTasks", "Number of tasks per client", numTasks);
  cmd.AddValue ("NetworkType", "Type of network (wifi/ethernet)", NetworkType);
  cmd.AddValue ("MaxPacketSize", "Maximum size packet that can be sent", MaxPacketSize);
  cmd.AddValue ("TxGain", "Power transmitted from clients and server", TxGain);
  cmd.AddValue ("ModelSize", "Size of model", ModelSize);
  cmd.AddValue ("DataRate", "Application data rate", dataRate);
  cmd.AddValue ("LearningModel", "Async or Sync federated learning", learningModel);

  cmd.Parse (argc, argv);

  bool bAsync = false;
  if (learningModel.compare ("async") == 0)
    {
      bAsync = true;
    }

  ModelSize = ModelSize * 1000; // conversion to bytes

  NS_LOG_UNCOND ("{NumClients:" << numClients
                                << ","
                                   "NumTasks:"
                                << numTasks
                                << ","
                                   "NetworkType:"
                                << NetworkType
                                << ","
                                   "MaxPacketSize:"
                                << MaxPacketSize
                                << ","
                                   "TxGain:"
                                << TxGain << "}");

  // Setup logging file
  std::time_t now = sysclock_t::to_time_t (sysclock_t::now ());

  char buf[80] = {0};
  std::strftime (buf, sizeof (buf), "%Y-%m-%d_%H-%M-%S.csv", std::localtime (&now));

  char strbuff[100];
  snprintf (strbuff, 99, "multi_task_%s_%s_%.2f_%s", learningModel.c_str (), NetworkType.c_str (),
            TxGain, buf);

  FILE *fp = fopen (strbuff, "w");
  if (fp)
    {
      fprintf (fp, "round,client_id,task_id,task_name,bandwidth,power,packet_loss,throughput,"
                   "latency,execution_time\n");
    }

  // Initialize multi-task client nodes
  for (int j = 0; j < numClients; j++)
    {
      // Place nodes at varying distances from base station
      double radius = (double) (10 << (j % 4 + 1)); // 20, 40, 80, 160 meters
      double theta = (2.0 * M_PI * j) / numClients; // Evenly distributed around circle

      NS_LOG_UNCOND ("INIT: Client " << j << " at radius=" << radius << " theta=" << theta);
      g_clientNodes[j] = std::shared_ptr<MultiTaskClientNode> (
          new MultiTaskClientNode (j, radius, theta, numTasks));
    }

  ns3::Time timeOffset (0);

  // Wait for connection from Python multi-task FL system
  NS_LOG_UNCOND ("Waiting for connection from Python multi-task FL system...");
  g_socketInterface.WaitForConnection ();

  int round = 0;

  while (true)
    {
      round++;
      NS_LOG_UNCOND ("========== Starting Multi-Task FL Round " << round << " ==========");

      // Receive task execution plan from Python
      MultiTaskFLSocketInterface::TaskExecutionPlan executionPlan =
          g_socketInterface.ReceiveTaskExecutionPlan (g_clientNodes);

      if (executionPlan.command == MultiTaskFLSocketInterface::COMMAND::EXIT)
        {
          NS_LOG_UNCOND ("Exit command received, terminating simulation");
          g_socketInterface.Close ();
          break;
        }

      if (executionPlan.command != MultiTaskFLSocketInterface::COMMAND::RUN_SIMULATION)
        {
          NS_LOG_UNCOND ("Invalid command received, skipping round");
          continue;
        }

      NS_LOG_UNCOND ("Received execution plan with " << executionPlan.taskExecutions.size ()
                                                     << " task executions for "
                                                     << executionPlan.numClients << " clients");

      // Log execution plan details
      for (const auto &taskExec : executionPlan.taskExecutions)
        {
          NS_LOG_UNCOND ("Task execution: Client "
                         << taskExec.clientId << " Task " << taskExec.taskName << " Priority="
                         << taskExec.priority << " T_ij=" << taskExec.computeTime << "s"
                         << " B_ij=" << taskExec.bandwidth << " P_ij=" << taskExec.power
                         << " Fs_ij=" << taskExec.fileSize << "MB");
        }

      // Create and run multi-task experiment
      auto experiment =
          MultiTaskFLExperiment (numClients, numTasks, NetworkType, MaxPacketSize, TxGain,
                                 ModelSize, dataRate, bAsync, &g_socketInterface, fp, round);

      // Run the experiment with the received execution plan
      auto results = experiment.RunMultiTaskExperiment (executionPlan, timeOffset);

      NS_LOG_UNCOND ("Experiment completed, sending results back to Python");

      // Clean up simulator state after each round
      ns3::Simulator::Destroy ();

      // Send results back to Python
      if (!bAsync)
        {
          g_socketInterface.SendResults (results);
        }

      // Log results to file
      if (fp)
        {
          for (const auto &taskResult : results.taskResults)
            {
              fprintf (fp, "%d,%d,%d,%s,%.6f,%.6f,%.6f,%.6f,%.6f,%.6f\n", round,
                       taskResult.clientId, taskResult.taskId, taskResult.taskName.c_str (),
                       taskResult.bandwidth, taskResult.power, taskResult.packetLoss,
                       taskResult.throughput, taskResult.latency, taskResult.executionTime);
            }
          fflush (fp);
        }

      // Log summary
      NS_LOG_UNCOND ("Round " << round << " Summary:");
      for (const auto &taskResult : results.taskResults)
        {
          NS_LOG_UNCOND ("  Client " << taskResult.clientId << " " << taskResult.taskName
                                     << ": Throughput=" << taskResult.throughput << "Mbps"
                                     << ", Latency=" << taskResult.latency << "ms"
                                     << ", PacketLoss=" << taskResult.packetLoss);
        }

      NS_LOG_UNCOND ("========== Multi-Task FL Round " << round << " Completed ==========");
    }

  if (fp)
    {
      fclose (fp);
    }

  NS_LOG_UNCOND ("Multi-Task FL simulation terminated");
  return 0;
}
