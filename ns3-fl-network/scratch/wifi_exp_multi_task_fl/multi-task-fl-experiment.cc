/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-fl-experiment.h"
#include "ns3/energy-module.h"
#include "ns3/wifi-radio-energy-model.h"
#include "ns3/basic-energy-source.h"
#include "ns3/simple-device-energy-model.h"
#include <algorithm>
#include <cmath>

namespace ns3 {

NS_LOG_COMPONENT_DEFINE ("MultiTaskFLExperiment");

MultiTaskFLExperiment::MultiTaskFLExperiment (int numClients, int numTasks,
                                              std::string &networkType, int maxPacketSize,
                                              double txGain, double modelSize,
                                              std::string &dataRate, bool bAsync,
                                              MultiTaskFLSocketInterface *socketInterface, FILE *fp,
                                              int round)
    : m_numClients (numClients),
      m_numTasks (numTasks),
      m_networkType (networkType),
      m_maxPacketSize (maxPacketSize),
      m_txGain (txGain),
      m_modelSize (modelSize),
      m_dataRate (dataRate),
      m_bAsync (bAsync),
      m_socketInterface (socketInterface),
      m_fp (fp),
      m_round (round),
      m_nextChannelIndex (0)
{

  // Initialize network configuration
  m_baseTxPower = 20.0 + m_txGain; // Base TX power in dBm
  m_maxBandwidth = 54.0; // Max bandwidth in Mbps for 802.11g

  // Initialize available WiFi channels for independent allocation
  m_availableChannels = GetAvailableChannels ();

  NS_LOG_INFO ("MultiTaskFLExperiment initialized: "
               << m_numClients << " clients, " << m_numTasks << " tasks, "
               << "network=" << m_networkType << ", round=" << m_round
               << ", available channels=" << m_availableChannels.size ());
}

MultiTaskFLSocketInterface::SimulationResults
MultiTaskFLExperiment::RunMultiTaskExperiment (
    const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan, ns3::Time &timeOffset)
{
  NS_LOG_INFO ("Starting multi-task FL experiment with " << executionPlan.taskExecutions.size ()
                                                         << " task executions");

  // Use the new concurrent multi-task implementation
  return RunConcurrentMultiTaskExperiment (executionPlan, timeOffset);
}

void
MultiTaskFLExperiment::SetPosition (Ptr<Node> node, double radius, double theta)
{
  Ptr<MobilityModel> mobility = node->GetObject<MobilityModel> ();
  Vector pos (radius * cos (theta), radius * sin (theta), 0);
  mobility->SetPosition (pos);
}

Vector
MultiTaskFLExperiment::GetPosition (Ptr<Node> node)
{
  Ptr<MobilityModel> mobility = node->GetObject<MobilityModel> ();
  return mobility->GetPosition ();
}

NetDeviceContainer
MultiTaskFLExperiment::SetupWifiNetwork (
    NodeContainer &nodes, const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan)
{

  NS_LOG_INFO ("Setting up WiFi network for multi-task FL");
  NS_LOG_DEBUG ("Creating WiFi channel and PHY layer...");

  // Create WiFi channel with proper initialization order
  NS_LOG_DEBUG ("Creating WiFi channel helper...");
  YansWifiChannelHelper channel = YansWifiChannelHelper::Default ();

  NS_LOG_DEBUG ("Setting up propagation loss and delay models...");
  channel.AddPropagationLoss ("ns3::FriisPropagationLossModel", "Frequency", DoubleValue (5.180e9));
  channel.SetPropagationDelay ("ns3::ConstantSpeedPropagationDelayModel");

  NS_LOG_DEBUG ("Creating WiFi PHY helper...");
  YansWifiPhyHelper phy = YansWifiPhyHelper ();

  NS_LOG_DEBUG ("Creating channel and setting it to PHY...");
  Ptr<YansWifiChannel> wifiChannel = channel.Create ();
  phy.SetChannel (wifiChannel);

  // Configure WiFi
  WifiHelper wifi;
  wifi.SetRemoteStationManager ("ns3::ConstantRateWifiManager", "DataMode",
                                StringValue ("OfdmRate54Mbps"), "ControlMode",
                                StringValue ("OfdmRate6Mbps"));

  WifiMacHelper mac;
  Ssid ssid = Ssid ("multi-task-fl-network");

  // Configure single AP (only one AP for all task servers)
  mac.SetType ("ns3::ApWifiMac", "Ssid", SsidValue (ssid));
  NetDeviceContainer serverDevices;

  // Create only ONE AP on the first server node
  NetDeviceContainer apDevice = wifi.Install (phy, mac, nodes.Get (m_numClients));
  serverDevices.Add (apDevice);

  NS_LOG_UNCOND ("Created single AP on node " << m_numClients << " for all task servers");

  // Configure clients (STAs)
  mac.SetType ("ns3::StaWifiMac", "Ssid", SsidValue (ssid), "ActiveProbing", BooleanValue (false));
  NetDeviceContainer staDevices;
  for (int i = 0; i < m_numClients; i++)
    {
      // Set default transmission power
      phy.Set ("TxPowerStart", DoubleValue (m_baseTxPower));
      phy.Set ("TxPowerEnd", DoubleValue (m_baseTxPower));

      NetDeviceContainer clientDevice = wifi.Install (phy, mac, nodes.Get (i));
      staDevices.Add (clientDevice);

      NS_LOG_UNCOND ("Client " << i
                               << " WiFi STA device configured with SSID: " << ssid.PeekString ());
    }

  // Combine all devices
  NetDeviceContainer allDevices;
  allDevices.Add (staDevices);
  allDevices.Add (serverDevices);

  return allDevices;
}

NetDeviceContainer
MultiTaskFLExperiment::SetupEthernetNetwork (
    NodeContainer &nodes, const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan)
{

  NS_LOG_INFO ("Setting up Ethernet network");

  CsmaHelper csma;
  csma.SetChannelAttribute ("DataRate", StringValue ("100Mbps"));
  csma.SetChannelAttribute ("Delay", TimeValue (NanoSeconds (6560)));

  NetDeviceContainer devices = csma.Install (nodes);
  return devices;
}

void
MultiTaskFLExperiment::ConfigureNetworkForTask (NodeContainer &nodes,
                                                const TaskExecution &taskExecution)
{
  // Configure network parameters for the specific client and task
  if (m_networkType == "wifi")
    {
      // TODO ConfigureWiFiForTask
      ConfigureWiFiForTask (nodes.Get (taskExecution.clientId), taskExecution);
    }
  else if (m_networkType == "ethernet")
    {
      // For Ethernet, we can configure traffic control
      ConfigureEthernetForTask (nodes.Get (taskExecution.clientId), taskExecution);
    }

  NS_LOG_INFO ("Network configured for Client "
               << taskExecution.clientId << " Task " << taskExecution.taskName
               << " (P_ij=" << taskExecution.power << ", B_ij=" << taskExecution.bandwidth << ")");
}

void
MultiTaskFLExperiment::ConfigureWiFiForTask (Ptr<Node> clientNode,
                                             const TaskExecution &taskExecution)
{
  NS_LOG_DEBUG ("Configuring WiFi for Client " << taskExecution.clientId << " Task "
                                               << taskExecution.taskName);

  Ptr<NetDevice> device = clientNode->GetDevice (0);

  if (device)
    {
      NS_LOG_DEBUG ("Device found, attempting to get TypeId...");
      try
        {
          TypeId deviceTypeId = device->GetInstanceTypeId ();
          std::string deviceTypeName = deviceTypeId.GetName ();
          NS_LOG_DEBUG ("Device TypeId retrieved successfully: " << deviceTypeName);

          if (deviceTypeName == "ns3::WifiNetDevice")
            {
              Ptr<WifiNetDevice> wifiDevice = DynamicCast<WifiNetDevice> (device);
              if (wifiDevice)
                {
                  Ptr<WifiPhy> phy = wifiDevice->GetPhy ();
                  if (phy)
                    {
                      // Configure transmission power based on P_ij
                      double txPower = ConfigureTransmissionPower (device, taskExecution.power);
                      // TODO SetPower
                      phy->SetTxPowerStart (txPower);
                      phy->SetTxPowerEnd (txPower);

                      // Assign unique channel for this client-task combination
                      ChannelConfig channelConfig =
                          AssignUniqueChannel (taskExecution.clientId, taskExecution.taskId);

                      // Configure the assigned channel
                      NS_LOG_INFO ("Configuring WiFi channel for Client "
                                   << taskExecution.clientId << " Task " << taskExecution.taskId
                                   << ": Channel " << (int) channelConfig.channelNumber << " ("
                                   << channelConfig.frequency << " MHz, "
                                   << channelConfig.channelWidth << " MHz width)");

                      try
                        {
                          // Set the operating channel with specific frequency and width
                          phy->SetOperatingChannel (channelConfig.channelNumber,
                                                    channelConfig.frequency,
                                                    channelConfig.channelWidth);

                          NS_LOG_INFO ("Successfully configured channel "
                                       << (int) channelConfig.channelNumber << " for Client "
                                       << taskExecution.clientId << " Task "
                                       << taskExecution.taskId);
                        }
                      catch (const std::exception &e)
                        {
                          NS_LOG_ERROR ("Failed to configure channel "
                                        << (int) channelConfig.channelNumber << " for Client "
                                        << taskExecution.clientId << " Task "
                                        << taskExecution.taskId << ": " << e.what ());

                          // Fallback to default channel
                          phy->SetOperatingChannel (36, 5180, 20);
                        }

                      // Also configure the MAC layer for better control
                      Ptr<WifiMac> mac = wifiDevice->GetMac ();
                      if (mac)
                        {
                          // Configure MAC parameters for independent channel operation
                          NS_LOG_DEBUG ("MAC layer configured for independent channel operation");
                        }

                      // Get the assigned channel for logging
                      ChannelConfig logChannelConfig =
                          AssignUniqueChannel (taskExecution.clientId, taskExecution.taskId);

                      NS_LOG_INFO ("WiFi configured for Client "
                                   << taskExecution.clientId << " Task " << taskExecution.taskName
                                   << ": TxPower=" << txPower << "dBm"
                                   << ", Channel=" << (int) logChannelConfig.channelNumber << " ("
                                   << logChannelConfig.frequency << "MHz, "
                                   << logChannelConfig.channelWidth << "MHz width)"
                                   << " (P_ij=" << taskExecution.power
                                   << ", B_ij=" << taskExecution.bandwidth << ")");

                      // Verify the configuration was applied
                      NS_LOG_DEBUG ("Verification: Current TxPowerStart="
                                    << phy->GetTxPowerStart ()
                                    << "dBm, ChannelWidth=" << phy->GetChannelWidth () << "MHz"
                                    << ", Frequency=" << phy->GetFrequency () << "MHz");
                    }
                }
            }
        }
      catch (const std::exception &e)
        {
          NS_LOG_ERROR ("Error configuring WiFi for task: " << e.what ());
        }
    }
}

void
MultiTaskFLExperiment::ConfigureEthernetForTask (Ptr<Node> clientNode,
                                                 const TaskExecution &taskExecution)
{
  Ptr<NetDevice> device = clientNode->GetDevice (0);

  if (device)
    {
      try
        {
          TypeId deviceTypeId = device->GetInstanceTypeId ();
          std::string deviceTypeName = deviceTypeId.GetName ();

          if (deviceTypeName == "ns3::CsmaNetDevice")
            {
              // For CSMA (Ethernet), we can configure traffic control
              // Note: Actual data rate is set at channel level during network setup

              NS_LOG_INFO ("Ethernet configured for Client "
                           << taskExecution.clientId << " Task " << taskExecution.taskName
                           << " (P_ij=" << taskExecution.power
                           << ", B_ij=" << taskExecution.bandwidth << ")");

              // For Ethernet, P_ij could represent priority and B_ij represents bandwidth allocation
              // These would typically be handled by traffic control mechanisms
            }
        }
      catch (const std::exception &e)
        {
          NS_LOG_ERROR ("Error configuring Ethernet for task: " << e.what ());
        }
    }
}

uint16_t
MultiTaskFLExperiment::ConfigureChannelWidth (Ptr<NetDevice> device, double bandwidth)
{
  // Map bandwidth allocation [0,1] to channel width in MHz
  // Higher bandwidth allocation gets wider channels

  double effectiveBandwidth = std::max (0.0, std::min (1.0, bandwidth));

  if (effectiveBandwidth >= 0.8)
    {
      return 80; // 80 MHz channel
    }
  else if (effectiveBandwidth >= 0.6)
    {
      return 40; // 40 MHz channel
    }
  else if (effectiveBandwidth >= 0.4)
    {
      return 20; // 20 MHz channel
    }
  else
    {
      return 10; // 10 MHz channel (minimum)
    }
}

double
MultiTaskFLExperiment::ConfigureTransmissionPower (Ptr<NetDevice> device, double power)
{
  // Map power allocation [0,1] to transmission power in dBm
  // Higher power allocation gets higher transmission power

  double effectivePower = std::max (0.0, std::min (1.0, power));

  // Power range: 5 dBm to 30 dBm
  double minPower = 5.0;
  double maxPower = 30.0;

  double txPower = minPower + (effectivePower * (maxPower - minPower));

  return txPower;
}

ApplicationContainer
MultiTaskFLExperiment::CreateFileTransferApplication (Ptr<Node> clientNode,
                                                      Ipv4Address serverAddress,
                                                      const TaskExecution &taskExecution,
                                                      uint16_t basePort)
{

  ApplicationContainer allApps;

  // Create server application (file receiver)
  uint16_t serverPort = basePort;
  PacketSinkHelper sinkHelper ("ns3::TcpSocketFactory",
                               InetSocketAddress (Ipv4Address::GetAny (), serverPort));
  ApplicationContainer serverApp = sinkHelper.Install (m_nodes.Get (m_numClients)); // Server node

  // Server should be ready before WiFi transmission starts
  serverApp.Start (Seconds (0.0));
  serverApp.Stop (Seconds (taskExecution.computeTime + 10.0)); // Run longer than total time
  allApps.Add (serverApp);

  // Create client application (file sender)
  BulkSendHelper bulkSendHelper ("ns3::TcpSocketFactory",
                                 InetSocketAddress (serverAddress, serverPort));

  // Calculate total data to send based on file size Fs_ij
  uint64_t totalBytes =
      static_cast<uint64_t> (taskExecution.fileSize * 1024 * 1024); // Convert MB to bytes
  bulkSendHelper.SetAttribute ("MaxBytes", UintegerValue (totalBytes));

  // Set packet size based on bandwidth allocation
  uint32_t packetSize =
      std::max (512u, std::min ((uint32_t) m_maxPacketSize,
                                (uint32_t) (m_maxPacketSize * taskExecution.bandwidth)));
  bulkSendHelper.SetAttribute ("SendSize", UintegerValue (packetSize));

  ApplicationContainer clientApp = bulkSendHelper.Install (clientNode);

  // IMPORTANT: WiFi transmission starts AFTER compute time T_ij
  // The timing will be set by the caller (RunSingleTaskSimulation)
  // Here we just create the application, timing is set externally

  allApps.Add (clientApp);

  NS_LOG_INFO ("Created file transfer application:");
  NS_LOG_INFO ("  File size: " << taskExecution.fileSize << " MB (" << totalBytes << " bytes)");
  NS_LOG_INFO ("  Packet size: " << packetSize << " bytes");
  NS_LOG_INFO ("  Compute time T_ij: " << taskExecution.computeTime << " seconds");
  NS_LOG_INFO ("  WiFi transmission will start AFTER T_ij computation");
  NS_LOG_INFO ("  Server port: " << serverPort);

  return allApps;
}

TaskResult
MultiTaskFLExperiment::CollectTaskResults (const TaskExecution &taskExecution,
                                           Ptr<FlowMonitor> monitor,
                                           Ptr<Ipv4FlowClassifier> classifier,
                                           Ipv4InterfaceContainer &interfaces, double actualTxPower,
                                           double actualBandwidth)
{

  TaskResult result;
  result.clientId = taskExecution.clientId;
  result.taskId = taskExecution.taskId;
  result.taskName = taskExecution.taskName;
  result.power = actualTxPower;
  result.bandwidth = actualBandwidth;
  result.executionTime = taskExecution.computeTime;

  // Initialize default values
  result.packetLoss = 0.0;
  result.throughput = 0.0;
  result.latency = 0.0;

  // Collect flow monitor statistics
  monitor->CheckForLostPackets ();
  std::map<FlowId, FlowMonitor::FlowStats> stats = monitor->GetFlowStats ();

  // Find flows for this client
  Ipv4Address clientAddress = interfaces.GetAddress (taskExecution.clientId);

  double totalThroughput = 0.0;
  double totalLatency = 0.0;
  double totalPacketLoss = 0.0;
  int flowCount = 0;

  for (auto &flowPair : stats)
    {
      Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow (flowPair.first);

      if (t.sourceAddress == clientAddress)
        {
          const FlowMonitor::FlowStats &flowStats = flowPair.second;

          if (flowStats.txPackets > 0)
            {
              // Calculate throughput (Mbps)
              double duration = flowStats.timeLastTxPacket.GetSeconds () -
                                flowStats.timeFirstTxPacket.GetSeconds ();
              if (duration > 0)
                {
                  double throughput = (flowStats.txBytes * 8.0) / (duration * 1000000.0); // Mbps
                  totalThroughput += throughput;
                }

              // Calculate latency (ms) - average per-packet delay
              if (flowStats.rxPackets > 0)
                {
                  double latency = flowStats.delaySum.GetMilliSeconds () / flowStats.rxPackets;
                  totalLatency += latency;
                }

              // Calculate packet loss ratio
              double packetLoss =
                  (double) (flowStats.txPackets - flowStats.rxPackets) / flowStats.txPackets;

              totalPacketLoss += packetLoss;

              flowCount++;

              NS_LOG_DEBUG ("Flow " << flowPair.first << ": "
                                    << "TX=" << flowStats.txPackets << ", RX="
                                    << flowStats.rxPackets << ", Bytes=" << flowStats.txBytes);
            }
        }
    }

  // Average the metrics
  if (flowCount > 0)
    {
      result.throughput = totalThroughput / flowCount;
      result.latency = totalLatency / flowCount;
      result.packetLoss = totalPacketLoss / flowCount;
    }

  // Ensure packet loss is in valid range [0,1]
  result.packetLoss = std::max (0.0, std::min (1.0, result.packetLoss));

  NS_LOG_INFO ("Task result collected:");
  NS_LOG_INFO ("  Client " << result.clientId << " Task " << result.taskName);
  NS_LOG_INFO ("  Bandwidth: " << result.bandwidth << " MHz");
  NS_LOG_INFO ("  Power: " << result.power << " dBm");
  NS_LOG_INFO ("  Throughput: " << result.throughput << " Mbps");
  NS_LOG_INFO ("  Latency: " << result.latency << " ms");
  NS_LOG_INFO ("  Packet Loss: " << result.packetLoss);
  NS_LOG_INFO ("  Execution Time: " << result.executionTime << " s");

  return result;
}

MultiTaskFLSocketInterface::SimulationResults
MultiTaskFLExperiment::RunConcurrentMultiTaskExperiment (
    const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan, ns3::Time &timeOffset)
{
  NS_LOG_INFO ("Starting concurrent multi-task FL experiment with "
               << executionPlan.taskExecutions.size () << " task executions");

  MultiTaskFLSocketInterface::SimulationResults results;

  // Create nodes (clients + 1 AP server)
  m_nodes.Create (m_numClients + 1); // Only need one AP node for all task servers

  // Position nodes
  MobilityHelper mobility;
  mobility.SetMobilityModel ("ns3::ConstantPositionMobilityModel");
  mobility.Install (m_nodes);

  // Set single AP server at center (0,0)
  SetPosition (m_nodes.Get (m_numClients), 0.0, 0.0);
  NS_LOG_INFO ("Single AP server positioned at center for all tasks");

  // Position clients in a circle around servers
  for (int i = 0; i < m_numClients; i++)
    {
      double radius = 20.0 + (i % 4) * 10.0; // Varying distances
      double theta = (2.0 * M_PI * i) / m_numClients;
      SetPosition (m_nodes.Get (i), radius, theta);

      NS_LOG_DEBUG ("Client " << i << " positioned at radius=" << radius << ", theta=" << theta);
    }

  // Setup network based on type
  if (m_networkType == "wifi")
    {
      m_devices = SetupWifiNetwork (m_nodes, executionPlan);
    }
  else
    {
      m_devices = SetupEthernetNetwork (m_nodes, executionPlan);
    }

  // Install Internet stack
  InternetStackHelper stack;
  stack.Install (m_nodes);

  // Assign IP addresses
  Ipv4AddressHelper address;
  address.SetBase ("********", "*************");
  m_interfaces = address.Assign (m_devices);

  // Debug: Print all IP addresses
  NS_LOG_UNCOND ("=== IP Address Assignment ===");
  for (uint32_t i = 0; i < m_interfaces.GetN (); i++)
    {
      NS_LOG_UNCOND ("Node " << i << " IP: " << m_interfaces.GetAddress (i));
    }

  // Enable routing
  Ipv4GlobalRoutingHelper::PopulateRoutingTables ();
  NS_LOG_UNCOND ("Global routing tables populated");

  // Setup flow monitoring
  FlowMonitorHelper flowHelper;
  Ptr<FlowMonitor> monitor = flowHelper.InstallAll ();
  Ptr<Ipv4FlowClassifier> classifier =
      DynamicCast<Ipv4FlowClassifier> (flowHelper.GetClassifier ());

  // Create all task servers (one for each task type)
  std::map<uint32_t, ApplicationContainer> taskServers =
      CreateAllTaskServers (m_nodes, m_interfaces, executionPlan.numTasks);

  // Schedule task execution with REAL task switching (no pre-created client apps)
  std::map<std::pair<uint32_t, uint32_t>, ApplicationContainer> emptyClientApps; // Not used anymore
  ScheduleTaskExecution (emptyClientApps, executionPlan);

  // Group tasks by client to calculate their total execution time
  std::map<uint32_t, std::vector<TaskExecution>> clientTasks;
  for (const auto &taskExecution : executionPlan.taskExecutions)
    {
      if (taskExecution.isSelected)
        {
          clientTasks[taskExecution.clientId].push_back (taskExecution);
        }
    }

  // Run simulation
  Simulator::Stop (Seconds (10000000.0));
  Simulator::Run ();

  // Collect results from all task executions
  results = CollectConcurrentTaskResults (executionPlan, monitor, classifier, m_interfaces);

  // Update time offset for next round
  timeOffset += Seconds (10000000.0);

  NS_LOG_INFO ("Concurrent multi-task FL experiment completed with " << results.taskResults.size ()
                                                                     << " task results");
  return results;
}

std::map<uint32_t, ApplicationContainer>
MultiTaskFLExperiment::CreateAllTaskServers (NodeContainer &nodes,
                                             Ipv4InterfaceContainer &interfaces, uint32_t numTasks)
{
  std::map<uint32_t, ApplicationContainer> taskServers;

  NS_LOG_UNCOND ("Creating " << numTasks << " task servers");

  // All task servers run on the SAME AP node (single server node)
  Ptr<Node> serverNode = nodes.Get (m_numClients); // Only use the first server node as AP

  for (uint32_t taskId = 0; taskId < numTasks; taskId++)
    {
      uint16_t serverPort = 9000 + taskId; // Different port for each task

      // Create packet sink (file receiver) for this task
      PacketSinkHelper sinkHelper ("ns3::TcpSocketFactory",
                                   InetSocketAddress (Ipv4Address::GetAny (), serverPort));
      ApplicationContainer serverApp = sinkHelper.Install (serverNode);

      // Start server immediately and keep running
      serverApp.Start (Seconds (0.0));
      serverApp.Stop (Seconds (10000000.0)); // Run for a long time

      taskServers[taskId] = serverApp;

      NS_LOG_UNCOND ("Task " << taskId << " server created on AP node " << m_numClients << " port "
                             << serverPort << " IP " << interfaces.GetAddress (m_numClients));
    }

  return taskServers;
}

std::map<std::pair<uint32_t, uint32_t>, ApplicationContainer>
MultiTaskFLExperiment::CreateClientApplications (
    NodeContainer &nodes, Ipv4InterfaceContainer &interfaces,
    const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan)
{
  std::map<std::pair<uint32_t, uint32_t>, ApplicationContainer> clientApps;

  NS_LOG_UNCOND ("Creating client applications for " << executionPlan.taskExecutions.size ()
                                                     << " task executions");

  for (const auto &taskExecution : executionPlan.taskExecutions)
    {
      if (taskExecution.isSelected)
        {
          // Get client node
          Ptr<Node> clientNode = nodes.Get (taskExecution.clientId);

          // Get server address (all tasks use the same AP node)
          Ipv4Address serverAddress = interfaces.GetAddress (m_numClients); // Same AP for all tasks
          uint16_t serverPort = 9000 + taskExecution.taskId;

          // Create bulk send application (file sender)
          BulkSendHelper bulkSendHelper ("ns3::TcpSocketFactory",
                                         InetSocketAddress (serverAddress, serverPort));

          // Calculate total data to send based on file size Fs_ij
          uint64_t totalBytes =
              static_cast<uint64_t> (taskExecution.fileSize * 1024 * 1024); // Convert MB to bytes
          bulkSendHelper.SetAttribute ("MaxBytes", UintegerValue (totalBytes));

          // Set packet size based on bandwidth allocation
          uint32_t packetSize =
              std::max (512u, std::min ((uint32_t) m_maxPacketSize,
                                        (uint32_t) (m_maxPacketSize * taskExecution.bandwidth)));
          bulkSendHelper.SetAttribute ("SendSize", UintegerValue (packetSize));

          ApplicationContainer clientApp = bulkSendHelper.Install (clientNode);

          // Store the application for later scheduling
          std::pair<uint32_t, uint32_t> key =
              std::make_pair (taskExecution.clientId, taskExecution.taskId);
          clientApps[key] = clientApp;

          NS_LOG_UNCOND ("Created client app: Client "
                         << taskExecution.clientId << " Task " << taskExecution.taskId
                         << " -> Server " << serverAddress << ":" << serverPort
                         << " FileSize=" << taskExecution.fileSize << "MB");
        }
    }

  return clientApps;
}

void
MultiTaskFLExperiment::ScheduleTaskExecution (
    const std::map<std::pair<uint32_t, uint32_t>, ApplicationContainer> &clientApps,
    const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan)
{
  NS_LOG_UNCOND ("Scheduling task execution with REAL task switching");

  // Group tasks by client to implement task switching
  std::map<uint32_t, std::vector<TaskExecution>> clientTasks;
  for (const auto &taskExecution : executionPlan.taskExecutions)
    {
      if (taskExecution.isSelected)
        {
          clientTasks[taskExecution.clientId].push_back (taskExecution);
        }
    }

  // Sort tasks by priority for each client (lowest priority first)
  for (auto &clientTaskPair : clientTasks)
    {
      std::sort (clientTaskPair.second.begin (), clientTaskPair.second.end (),
                 [] (const TaskExecution &a, const TaskExecution &b) {
                   return a.priority < b.priority; // Lower priority first
                 });
    }

  // Schedule task switching for each client
  for (auto &clientTaskPair : clientTasks)
    {
      uint32_t clientId = clientTaskPair.first;
      std::vector<TaskExecution> &tasks = clientTaskPair.second;

      NS_LOG_UNCOND ("Client " << clientId << " will execute " << tasks.size ()
                               << " tasks in priority order with server switching");

      // Use the new task switching scheduler
      ScheduleClientTaskSwitching (clientId, tasks, m_interfaces);
    }
}

void
MultiTaskFLExperiment::ScheduleClientTaskSwitching (uint32_t clientId,
                                                    std::vector<TaskExecution> &tasks,
                                                    Ipv4InterfaceContainer &interfaces)
{
  NS_LOG_UNCOND ("=== Client " << clientId << " Task Switching Schedule ===");

  for (size_t i = 0; i < tasks.size (); i++)
    {
      TaskExecution &taskExecution = tasks[i];

      // Calculate timing for this task
      double computeStartTime = taskExecution.priority * 10;
      taskExecution.startTime = computeStartTime;
      double transmissionStartTime = computeStartTime + taskExecution.computeTime;

      // Use ESTIMATED duration for scheduling next task (but don't force stop current task)
      // double estimatedTaskEndTime = transmissionStartTime + (taskExecution.estimatedDuration - taskExecution.computeTime);

      // Add buffer time between tasks to avoid overlap
      // double taskEndTimeWithBuffer = estimatedTaskEndTime + 10.0; // 10 second buffer

      NS_LOG_UNCOND ("Client " << clientId << " Task " << taskExecution.taskId
                               << " (priority=" << taskExecution.priority << "):");
      NS_LOG_UNCOND ("  Compute: " << computeStartTime << "s - " << transmissionStartTime << "s");
      NS_LOG_UNCOND ("  Connect to Server " << taskExecution.taskId << " (port "
                                            << (9000 + taskExecution.taskId) << ")");
      NS_LOG_UNCOND ("  Transmit: starts at " << transmissionStartTime << "s (natural completion)");
      // NS_LOG_UNCOND ("  Next task scheduled at: " << taskEndTimeWithBuffer << "s");

      // Schedule network configuration at compute start time
      Simulator::Schedule (Seconds (0), &MultiTaskFLExperiment::ConfigureNetworkForTask, this,
                           m_nodes, taskExecution);

      // Schedule task application creation and start at transmission time
      Simulator::Schedule (Seconds (0.01), &MultiTaskFLExperiment::StartSingleTask, this, clientId,
                           taskExecution, interfaces, transmissionStartTime);
    }
}

void
MultiTaskFLExperiment::StartSingleTask (uint32_t clientId, const TaskExecution &taskExecution,
                                        Ipv4InterfaceContainer &interfaces, double stopTime)
{
  NS_LOG_UNCOND ("TASK SWITCH: Client " << clientId << " starting Task " << taskExecution.taskId
                                        << " -> connecting to Server " << taskExecution.taskId);

  // Get client node
  Ptr<Node> clientNode = m_nodes.Get (clientId);

  // Get server address (all tasks use the same AP node)
  Ipv4Address serverAddress = interfaces.GetAddress (m_numClients); // Same AP for all tasks
  uint16_t serverPort = 9000 + taskExecution.taskId;

  NS_LOG_UNCOND ("  Server address: " << serverAddress << ":" << serverPort);

  // Create bulk send application for this specific task
  BulkSendHelper bulkSendHelper ("ns3::TcpSocketFactory",
                                 InetSocketAddress (serverAddress, serverPort));

  // Configure application parameters
  uint64_t totalBytes = static_cast<uint64_t> (taskExecution.fileSize * 1024 * 1024);
  bulkSendHelper.SetAttribute ("MaxBytes", UintegerValue (totalBytes));

  uint32_t packetSize =
      std::max (512u, std::min ((uint32_t) m_maxPacketSize,
                                (uint32_t) (m_maxPacketSize * taskExecution.bandwidth)));
  bulkSendHelper.SetAttribute ("SendSize", UintegerValue (packetSize));

  NS_LOG_UNCOND ("  TCP connection: " << clientId << " -> " << serverAddress << ":" << serverPort);
  NS_LOG_UNCOND ("  Data to send: " << totalBytes << " bytes, Packet size: " << packetSize);

  // Install and start the application immediately
  ApplicationContainer taskApp = bulkSendHelper.Install (clientNode);

  // Start application immediately and let it run until all data is transmitted
  taskApp.Start (Seconds (taskExecution.startTime + taskExecution.computeTime));

  // Set a very generous stop time - let the application complete naturally
  // The application will stop automatically when all data is sent
  double maxRunTime = 3600.0; // 1 hour - essentially unlimited
  taskApp.Stop (Seconds (maxRunTime));

  NS_LOG_UNCOND ("  Application started");
  NS_LOG_UNCOND ("  Will transmit " << (totalBytes / 1024 / 1024)
                                    << " MB and stop naturally when complete");
  NS_LOG_UNCOND ("  MaxBytes set to: " << totalBytes << " bytes");
}

MultiTaskFLSocketInterface::SimulationResults
MultiTaskFLExperiment::CollectConcurrentTaskResults (
    const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan, Ptr<FlowMonitor> monitor,
    Ptr<Ipv4FlowClassifier> classifier, Ipv4InterfaceContainer &interfaces)
{
  MultiTaskFLSocketInterface::SimulationResults results;

  NS_LOG_INFO ("Collecting results from concurrent task execution");

  // Collect flow monitor statistics
  monitor->CheckForLostPackets ();
  std::map<FlowId, FlowMonitor::FlowStats> stats = monitor->GetFlowStats ();

  NS_LOG_UNCOND ("=== Flow Monitor Statistics ===");
  NS_LOG_UNCOND ("Total flows detected: " << stats.size ());

  // Debug: Print all flows
  for (auto &flowPair : stats)
    {
      Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow (flowPair.first);
      const FlowMonitor::FlowStats &flowStats = flowPair.second;

      NS_LOG_UNCOND ("Flow " << flowPair.first << ": " << t.sourceAddress << ":" << t.sourcePort
                             << " -> " << t.destinationAddress << ":" << t.destinationPort
                             << " TX=" << flowStats.txPackets << " RX=" << flowStats.rxPackets);
    }

  for (const auto &taskExecution : executionPlan.taskExecutions)
    {
      if (taskExecution.isSelected)
        {
          TaskResult result;
          result.clientId = taskExecution.clientId;
          result.taskId = taskExecution.taskId;
          result.taskName = taskExecution.taskName;
          result.executionTime = taskExecution.computeTime;

          // Calculate actual network parameters
          double actualTxPower = ConfigureTransmissionPower (nullptr, taskExecution.power);
          double actualBandwidth = 0.0;

          // Map bandwidth to channel width
          if (taskExecution.bandwidth >= 0.8)
            {
              actualBandwidth = 80.0;
            }
          else if (taskExecution.bandwidth >= 0.6)
            {
              actualBandwidth = 40.0;
            }
          else if (taskExecution.bandwidth >= 0.4)
            {
              actualBandwidth = 20.0;
            }
          else
            {
              actualBandwidth = 10.0;
            }

          result.power = actualTxPower;
          result.bandwidth = actualBandwidth;

          // Initialize default values
          result.packetLoss = 0.0;
          result.throughput = 0.0;
          result.latency = 0.0;

          // Find flows for this client
          Ipv4Address clientAddress = interfaces.GetAddress (taskExecution.clientId);
          Ipv4Address serverAddress = interfaces.GetAddress (m_numClients); // Same AP for all tasks

          NS_LOG_UNCOND ("Looking for flows: Client "
                         << taskExecution.clientId << " (" << clientAddress << ") -> Server "
                         << taskExecution.taskId << " (" << serverAddress << ")");

          double totalThroughput = 0.0;
          double totalLatency = 0.0;
          double totalPacketLoss = 0.0;
          int flowCount = 0;

          for (auto &flowPair : stats)
            {
              Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow (flowPair.first);

              // Match flows for this specific client-server pair
              if (t.sourceAddress == clientAddress && t.destinationAddress == serverAddress)
                {
                  NS_LOG_UNCOND ("  Found matching flow " << flowPair.first);

                  const FlowMonitor::FlowStats &flowStats = flowPair.second;

                  NS_LOG_UNCOND ("    TX packets: " << flowStats.txPackets
                                                    << ", RX packets: " << flowStats.rxPackets);

                  if (flowStats.txPackets > 0)
                    {
                      // Calculate throughput (Mbps)
                      double duration = flowStats.timeLastTxPacket.GetSeconds () -
                                        flowStats.timeFirstTxPacket.GetSeconds ();
                      if (duration > 0)
                        {
                          double throughput =
                              (flowStats.txBytes * 8.0) / (duration * 1000000.0); // Mbps
                          totalThroughput += throughput;
                        }

                      // Calculate latency (ms) - average per-packet delay
                      if (flowStats.rxPackets > 0)
                        {
                          double latency =
                              flowStats.delaySum.GetMilliSeconds () / flowStats.rxPackets;
                          totalLatency += latency;
                        }

                      // Calculate packet loss ratio
                      double packetLoss = (double) (flowStats.txPackets - flowStats.rxPackets) /
                                          flowStats.txPackets;
                      totalPacketLoss += packetLoss;

                      flowCount++;
                    }
                }
            }

          // Average the metrics
          if (flowCount > 0)
            {
              result.throughput = totalThroughput / flowCount;
              result.latency = totalLatency / flowCount;
              result.packetLoss = totalPacketLoss / flowCount;

              NS_LOG_UNCOND ("  Found " << flowCount << " matching flows");
            }
          else
            {
              NS_LOG_ERROR ("  NO MATCHING FLOWS FOUND! This explains 100% packet loss.");
              NS_LOG_ERROR ("  Expected: " << clientAddress << " -> " << serverAddress);
              result.packetLoss = 1.0; // 100% loss if no flows found
            }

          // Ensure packet loss is in valid range [0,1]
          result.packetLoss = std::max (0.0, std::min (1.0, result.packetLoss));

          results.taskResults.push_back (result);

          NS_LOG_INFO ("Collected result for Client "
                       << result.clientId << " Task " << result.taskName
                       << ": Throughput=" << result.throughput << "Mbps, Latency=" << result.latency
                       << "ms, PacketLoss=" << result.packetLoss);
        }
    }

  return results;
}

std::vector<MultiTaskFLExperiment::ChannelConfig>
MultiTaskFLExperiment::GetAvailableChannels ()
{
  std::vector<ChannelConfig> channels;

  // Define available WiFi channels in 5 GHz band for independent allocation
  // Using non-overlapping channels to minimize interference

  // 20 MHz channels
  channels.push_back ({36, 5180, 20}); // Channel 36: 5180 MHz, 20 MHz width
  channels.push_back ({40, 5200, 20}); // Channel 40: 5200 MHz, 20 MHz width
  channels.push_back ({44, 5220, 20}); // Channel 44: 5220 MHz, 20 MHz width
  channels.push_back ({48, 5240, 20}); // Channel 48: 5240 MHz, 20 MHz width
  channels.push_back ({52, 5260, 20}); // Channel 52: 5260 MHz, 20 MHz width
  channels.push_back ({56, 5280, 20}); // Channel 56: 5280 MHz, 20 MHz width
  channels.push_back ({60, 5300, 20}); // Channel 60: 5300 MHz, 20 MHz width
  channels.push_back ({64, 5320, 20}); // Channel 64: 5320 MHz, 20 MHz width

  // 40 MHz channels (for higher bandwidth tasks)
  channels.push_back ({38, 5190, 40}); // Channel 38: 5190 MHz, 40 MHz width
  channels.push_back ({46, 5230, 40}); // Channel 46: 5230 MHz, 40 MHz width
  channels.push_back ({54, 5270, 40}); // Channel 54: 5270 MHz, 40 MHz width
  channels.push_back ({62, 5310, 40}); // Channel 62: 5310 MHz, 40 MHz width

  // 80 MHz channels (for very high bandwidth tasks)
  channels.push_back ({42, 5210, 80}); // Channel 42: 5210 MHz, 80 MHz width
  channels.push_back ({58, 5290, 80}); // Channel 58: 5290 MHz, 80 MHz width

  // Additional 2.4 GHz channels for more options (if needed)
  channels.push_back ({1, 2412, 20}); // Channel 1: 2412 MHz, 20 MHz width
  channels.push_back ({6, 2437, 20}); // Channel 6: 2437 MHz, 20 MHz width
  channels.push_back ({11, 2462, 20}); // Channel 11: 2462 MHz, 20 MHz width

  NS_LOG_INFO ("Initialized " << channels.size ()
                              << " available WiFi channels for independent allocation");

  return channels;
}

MultiTaskFLExperiment::ChannelConfig
MultiTaskFLExperiment::AssignUniqueChannel (uint32_t clientId, uint32_t taskId)
{
  // Create unique key for client-task combination
  std::pair<uint32_t, uint32_t> clientTaskKey = std::make_pair (clientId, taskId);

  // Check if channel already assigned for this client-task combination
  auto it = m_channelAssignments.find (clientTaskKey);
  if (it != m_channelAssignments.end ())
    {
      NS_LOG_DEBUG ("Reusing existing channel assignment for Client "
                    << clientId << " Task " << taskId << ": Channel "
                    << (int) it->second.channelNumber);
      return it->second;
    }

  // Assign new channel using round-robin allocation
  if (m_availableChannels.empty ())
    {
      NS_LOG_ERROR ("No available channels for assignment!");
      // Return default channel as fallback
      return {36, 5180, 20};
    }

  // Use round-robin to distribute channels evenly
  ChannelConfig assignedChannel = m_availableChannels[m_nextChannelIndex];
  m_nextChannelIndex = (m_nextChannelIndex + 1) % m_availableChannels.size ();

  // Store the assignment
  m_channelAssignments[clientTaskKey] = assignedChannel;

  NS_LOG_INFO ("Assigned unique channel to Client "
               << clientId << " Task " << taskId << ": Channel "
               << (int) assignedChannel.channelNumber << " (" << assignedChannel.frequency
               << " MHz, " << assignedChannel.channelWidth << " MHz width)");

  return assignedChannel;
}

} // namespace ns3
