/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-fl-experiment.h"
#include "ns3/energy-module.h"
#include "ns3/wifi-radio-energy-model.h"
#include "ns3/basic-energy-source.h"
#include "ns3/simple-device-energy-model.h"
#include <algorithm>
#include <cmath>

namespace ns3 {

    NS_LOG_COMPONENT_DEFINE("MultiTaskFLExperiment");

    MultiTaskFLExperiment::MultiTaskFLExperiment(int numClients, int numTasks, std::string &networkType, 
                        int maxPacketSize, double txGain, double modelSize,
                        std::string &dataRate, bool bAsync, 
                        MultiTaskFLSocketInterface *socketInterface, FILE *fp, int round)
        : m_numClients(numClients), m_numTasks(numTasks), m_networkType(networkType),
          m_maxPacketSize(maxPacketSize), m_txGain(txGain), m_modelSize(modelSize),
          m_dataRate(dataRate), m_bAsync(bAsync), m_socketInterface(socketInterface),
          m_fp(fp), m_round(round) {
        
        // Initialize network configuration
        m_baseTxPower = 20.0 + m_txGain;  // Base TX power in dBm
        m_maxBandwidth = 54.0;            // Max bandwidth in Mbps for 802.11g
        
        NS_LOG_INFO("MultiTaskFLExperiment initialized: " 
                   << m_numClients << " clients, " << m_numTasks << " tasks, "
                   << "network=" << m_networkType << ", round=" << m_round);
    }

    MultiTaskFLSocketInterface::SimulationResults
    MultiTaskFLExperiment::RunMultiTaskExperiment(const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan, 
                         ns3::Time &timeOffset) {
        
        NS_LOG_INFO("Starting multi-task FL experiment with " << executionPlan.taskExecutions.size() << " task executions");
        
        MultiTaskFLSocketInterface::SimulationResults results;
        
        // Create nodes (clients + 1 server)
        m_nodes.Create(m_numClients + 1);
        
        // Server is the last node
        Ptr<Node> serverNode = m_nodes.Get(m_numClients);
        
        // Position nodes
        MobilityHelper mobility;
        mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
        mobility.Install(m_nodes);
        
        // Set server at center (0,0)
        SetPosition(serverNode, 0.0, 0.0);
        
        // Position clients in a circle around server
        for (int i = 0; i < m_numClients; i++) {
            double radius = 20.0 + (i % 4) * 10.0;  // Varying distances
            double theta = (2.0 * M_PI * i) / m_numClients;
            SetPosition(m_nodes.Get(i), radius, theta);
            
            NS_LOG_DEBUG("Client " << i << " positioned at radius=" << radius << ", theta=" << theta);
        }
        
        // Setup network based on type
        if (m_networkType == "wifi") {
            m_devices = SetupWifiNetwork(m_nodes, executionPlan);
        } else {
            m_devices = SetupEthernetNetwork(m_nodes, executionPlan);
        }
        
        // Install Internet stack
        InternetStackHelper stack;
        stack.Install(m_nodes);
        
        // Assign IP addresses
        Ipv4AddressHelper address;
        address.SetBase("********", "*************");
        m_interfaces = address.Assign(m_devices);
        
        // Setup flow monitoring
        FlowMonitorHelper flowHelper;
        Ptr<FlowMonitor> monitor = flowHelper.InstallAll();
        Ptr<Ipv4FlowClassifier> classifier = DynamicCast<Ipv4FlowClassifier>(flowHelper.GetClassifier());
        
        // Execute tasks sequentially according to priority order
        NS_LOG_INFO("Executing tasks in priority order...");
        
        for (const auto& taskExecution : executionPlan.taskExecutions) {
            if (taskExecution.isSelected) {
                NS_LOG_INFO("Executing Client " << taskExecution.clientId 
                           << " Task " << taskExecution.taskName 
                           << " (priority=" << taskExecution.priority << ")");
                
                // Run single task simulation
                TaskResult taskResult = RunSingleTaskSimulation(
                    m_nodes, m_interfaces, taskExecution, monitor, classifier);
                
                results.taskResults.push_back(taskResult);
                
                NS_LOG_INFO("Task completed: throughput=" << taskResult.throughput 
                           << " Mbps, latency=" << taskResult.latency << " ms");
            }
        }
        
        // Update time offset for next round
        double maxEndTime = 0.0;
        for (const auto& taskExec : executionPlan.taskExecutions) {
            double endTime = taskExec.startTime + taskExec.duration;
            maxEndTime = std::max(maxEndTime, endTime);
        }
        timeOffset += Seconds(maxEndTime + 2.0);

        NS_LOG_INFO("Multi-task FL experiment completed with " << results.taskResults.size() << " task results");
        return results;
    }

    void MultiTaskFLExperiment::SetPosition(Ptr<Node> node, double radius, double theta) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        Vector pos(radius * cos(theta), radius * sin(theta), 0);
        mobility->SetPosition(pos);
    }

    Vector MultiTaskFLExperiment::GetPosition(Ptr<Node> node) {
        Ptr<MobilityModel> mobility = node->GetObject<MobilityModel>();
        return mobility->GetPosition();
    }

    NetDeviceContainer MultiTaskFLExperiment::SetupWifiNetwork(NodeContainer &nodes,
                                                             const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan) {

        NS_LOG_INFO("Setting up WiFi network for multi-task FL");
        NS_LOG_DEBUG("Creating WiFi channel and PHY layer...");

        // Create WiFi channel with proper initialization order
        NS_LOG_DEBUG("Creating WiFi channel helper...");
        YansWifiChannelHelper channel = YansWifiChannelHelper::Default();

        NS_LOG_DEBUG("Setting up propagation loss and delay models...");
        channel.AddPropagationLoss("ns3::FriisPropagationLossModel", "Frequency", DoubleValue(5.180e9));
        channel.SetPropagationDelay("ns3::ConstantSpeedPropagationDelayModel");

        NS_LOG_DEBUG("Creating WiFi PHY helper...");
        YansWifiPhyHelper phy = YansWifiPhyHelper();

        NS_LOG_DEBUG("Creating channel and setting it to PHY...");
        Ptr<YansWifiChannel> wifiChannel = channel.Create();
        phy.SetChannel(wifiChannel);

        // Configure WiFi
        WifiHelper wifi;
        wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                    "DataMode", StringValue("OfdmRate54Mbps"),
                                    "ControlMode", StringValue("OfdmRate6Mbps"));

        WifiMacHelper mac;
        Ssid ssid = Ssid("multi-task-fl-network");

        // Configure server (AP)
        mac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));
        NetDeviceContainer apDevice = wifi.Install(phy, mac, nodes.Get(m_numClients));

        // Configure clients (STAs)
        mac.SetType("ns3::StaWifiMac", "Ssid", SsidValue(ssid), "ActiveProbing", BooleanValue(false));
        
        NetDeviceContainer staDevices;
        for (int i = 0; i < m_numClients; i++) {
            // Set default transmission power
            phy.Set("TxPowerStart", DoubleValue(m_baseTxPower));
            phy.Set("TxPowerEnd", DoubleValue(m_baseTxPower));
            
            NetDeviceContainer clientDevice = wifi.Install(phy, mac, nodes.Get(i));
            staDevices.Add(clientDevice);
            
            NS_LOG_INFO("Client " << i << " WiFi device configured");
        }

        // Combine all devices
        NetDeviceContainer allDevices;
        allDevices.Add(staDevices);
        allDevices.Add(apDevice);

        return allDevices;
    }

    NetDeviceContainer MultiTaskFLExperiment::SetupEthernetNetwork(NodeContainer &nodes, 
                                                                 const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan) {
        
        NS_LOG_INFO("Setting up Ethernet network");

        CsmaHelper csma;
        csma.SetChannelAttribute("DataRate", StringValue("100Mbps"));
        csma.SetChannelAttribute("Delay", TimeValue(NanoSeconds(6560)));

        NetDeviceContainer devices = csma.Install(nodes);
        return devices;
    }

    TaskResult MultiTaskFLExperiment::RunSingleTaskSimulation(NodeContainer &nodes,
                                                            Ipv4InterfaceContainer &interfaces,
                                                            const TaskExecution &taskExecution,
                                                            Ptr<FlowMonitor> monitor,
                                                            Ptr<Ipv4FlowClassifier> classifier) {

        NS_LOG_INFO("Running simulation for Client " << taskExecution.clientId
                   << " Task " << taskExecution.taskName);

        // Step 1: Configure network for this specific task
        ConfigureNetworkForTask(nodes, taskExecution);

        // Verify configuration was applied with safety checks
        Ptr<NetDevice> clientDevice = nodes.Get(taskExecution.clientId)->GetDevice(0);
        NS_LOG_DEBUG("Attempting to verify device configuration for client " << taskExecution.clientId);

        if (clientDevice) {
            NS_LOG_DEBUG("Client device found, checking TypeId...");
            try {
                TypeId deviceTypeId = clientDevice->GetInstanceTypeId();
                std::string deviceTypeName = deviceTypeId.GetName();
                NS_LOG_DEBUG("Device type successfully retrieved: " << deviceTypeName);

                if (deviceTypeName == "ns3::WifiNetDevice") {
                    Ptr<WifiNetDevice> wifiDevice = DynamicCast<WifiNetDevice>(clientDevice);
                    if (wifiDevice) {
                        Ptr<WifiPhy> phy = wifiDevice->GetPhy();
                        if (phy) {
                            NS_LOG_INFO("VERIFICATION: Client " << taskExecution.clientId
                                       << " actual TxPower=" << phy->GetTxPowerStart() << "dBm, "
                                       << "ChannelWidth=" << phy->GetChannelWidth() << "MHz");
                        }
                    }
                }
            } catch (const std::exception& e) {
                NS_LOG_ERROR("Error getting device TypeId: " << e.what());
            }
        }

        // Step 2: Create file transfer application based on task parameters
        uint16_t basePort = 9000 + taskExecution.taskId;
        ApplicationContainer apps = CreateFileTransferApplication(
            nodes.Get(taskExecution.clientId),
            interfaces.GetAddress(m_numClients), // Server address
            taskExecution,
            basePort
        );

        // Step 3: Run simulation with proper timing
        // T_ij is compute time, after which WiFi transmission starts
        double computeTime = taskExecution.computeTime;  // T_ij: device computation time
        double transmissionTime = taskExecution.duration - taskExecution.computeTime;  // Estimated WiFi transmission time
        double totalSimTime = computeTime + transmissionTime + 1.0; // Add buffer

        NS_LOG_INFO("Starting task simulation for " << totalSimTime << " seconds");
        NS_LOG_INFO("Task timeline: Compute=" << computeTime << "s, WiFi transmission, then total");
        NS_LOG_INFO("Task parameters: T_ij=" << taskExecution.computeTime
                   << "s, B_ij=" << taskExecution.bandwidth
                   << ", P_ij=" << taskExecution.power
                   << ", Fs_ij=" << taskExecution.fileSize << "MB");

        // Clear previous flow monitor data
        monitor->CheckForLostPackets();

        // Start applications AFTER compute time T_ij
        // Device computes for T_ij seconds, then starts WiFi transmission
        apps.Start(Seconds(computeTime + 0.5));  // Start WiFi after T_ij + small buffer
        apps.Stop(Seconds(totalSimTime - 0.5));

        NS_LOG_INFO("WiFi transmission will start at " << (computeTime + 0.5) << "s (after T_ij computation)");

        // Run simulation
        Simulator::Stop(Seconds(totalSimTime));
        Simulator::Run();

        // Step 4: Collect results for this task
        // Calculate actual values (without modifying the device again)
        double actualTxPower = 5.0 + (taskExecution.power * 25.0); // 5-30 dBm range
        double actualBandwidth = 0.0;

        // Map bandwidth to channel width without modifying device
        if (taskExecution.bandwidth >= 0.8) {
            actualBandwidth = 80.0;
        } else if (taskExecution.bandwidth >= 0.6) {
            actualBandwidth = 40.0;
        } else if (taskExecution.bandwidth >= 0.4) {
            actualBandwidth = 20.0;
        } else {
            actualBandwidth = 10.0;
        }

        TaskResult result = CollectTaskResults(taskExecution, monitor, classifier,
                                             interfaces, actualTxPower, actualBandwidth);

        // Stop and clean up applications
        apps.Stop(Seconds(0));

        NS_LOG_INFO("Task simulation completed");
        return result;
    }

    void MultiTaskFLExperiment::ConfigureNetworkForTask(NodeContainer &nodes, const TaskExecution &taskExecution) {
        // Configure network parameters for the specific client and task
        if (m_networkType == "wifi") {
            ConfigureWiFiForTask(nodes.Get(taskExecution.clientId), taskExecution);
        } else if (m_networkType == "ethernet") {
            // For Ethernet, we can configure traffic control
            ConfigureEthernetForTask(nodes.Get(taskExecution.clientId), taskExecution);
        }

        NS_LOG_INFO("Network configured for Client " << taskExecution.clientId
                   << " Task " << taskExecution.taskName
                   << " (P_ij=" << taskExecution.power << ", B_ij=" << taskExecution.bandwidth << ")");
    }

    void MultiTaskFLExperiment::ConfigureWiFiForTask(Ptr<Node> clientNode, const TaskExecution &taskExecution) {
        NS_LOG_DEBUG("Configuring WiFi for Client " << taskExecution.clientId << " Task " << taskExecution.taskName);

        Ptr<NetDevice> device = clientNode->GetDevice(0);

        if (device) {
            NS_LOG_DEBUG("Device found, attempting to get TypeId...");
            try {
                TypeId deviceTypeId = device->GetInstanceTypeId();
                std::string deviceTypeName = deviceTypeId.GetName();
                NS_LOG_DEBUG("Device TypeId retrieved successfully: " << deviceTypeName);

                if (deviceTypeName == "ns3::WifiNetDevice") {
                    Ptr<WifiNetDevice> wifiDevice = DynamicCast<WifiNetDevice>(device);
                    if (wifiDevice) {
                        Ptr<WifiPhy> phy = wifiDevice->GetPhy();
                        if (phy) {
                            // Configure transmission power based on P_ij
                            double txPower = ConfigureTransmissionPower(device, taskExecution.power);
                            phy->SetTxPowerStart(txPower);
                            phy->SetTxPowerEnd(txPower);

                            // Configure channel width and operating channel based on B_ij
                            uint16_t channelWidth = ConfigureChannelWidth(device, taskExecution.bandwidth);

                            // Set complete channel configuration to avoid "channel width does not uniquely identify" error
                            // Use SetOperatingChannel(number, frequency, width) method
                            if (channelWidth == 80) {
                                // Channel 42 (5210 MHz) with 80 MHz width
                                phy->SetOperatingChannel(42, 5210, 80);
                            } else if (channelWidth == 40) {
                                // Channel 38 (5190 MHz) with 40 MHz width
                                phy->SetOperatingChannel(38, 5190, 40);
                            } else if (channelWidth == 20) {
                                // Channel 36 (5180 MHz) with 20 MHz width
                                phy->SetOperatingChannel(36, 5180, 20);
                            } else {
                                // For 10MHz or other widths, use 20MHz channel
                                phy->SetOperatingChannel(36, 5180, 20);
                            }

                            // Also configure the MAC layer for better control
                            Ptr<WifiMac> mac = wifiDevice->GetMac();
                            if (mac) {
                                // Set queue size based on bandwidth allocation
                                // uint32_t queueSize = std::max(10u, (uint32_t)(taskExecution.bandwidth * 1000));
                                // Note: Queue configuration would need traffic control layer
                            }

                            NS_LOG_INFO("WiFi configured for Client " << taskExecution.clientId
                                       << " Task " << taskExecution.taskName << ": TxPower=" << txPower
                                       << "dBm, ChannelWidth=" << channelWidth << "MHz"
                                       << " (P_ij=" << taskExecution.power << ", B_ij=" << taskExecution.bandwidth << ")");

                            // Verify the configuration was applied
                            NS_LOG_DEBUG("Verification: Current TxPowerStart=" << phy->GetTxPowerStart()
                                        << "dBm, ChannelWidth=" << phy->GetChannelWidth() << "MHz");
                        }
                    }
                }
            } catch (const std::exception& e) {
                NS_LOG_ERROR("Error configuring WiFi for task: " << e.what());
            }
        }
    }

    void MultiTaskFLExperiment::ConfigureEthernetForTask(Ptr<Node> clientNode, const TaskExecution &taskExecution) {
        Ptr<NetDevice> device = clientNode->GetDevice(0);

        if (device) {
            try {
                TypeId deviceTypeId = device->GetInstanceTypeId();
                std::string deviceTypeName = deviceTypeId.GetName();

                if (deviceTypeName == "ns3::CsmaNetDevice") {
                    // For CSMA (Ethernet), we can configure traffic control
                    // Note: Actual data rate is set at channel level during network setup

                    NS_LOG_INFO("Ethernet configured for Client " << taskExecution.clientId
                               << " Task " << taskExecution.taskName
                               << " (P_ij=" << taskExecution.power << ", B_ij=" << taskExecution.bandwidth << ")");

                    // For Ethernet, P_ij could represent priority and B_ij represents bandwidth allocation
                    // These would typically be handled by traffic control mechanisms
                }
            } catch (const std::exception& e) {
                NS_LOG_ERROR("Error configuring Ethernet for task: " << e.what());
            }
        }
    }

    uint16_t MultiTaskFLExperiment::ConfigureChannelWidth(Ptr<NetDevice> device, double bandwidth) {
        // Map bandwidth allocation [0,1] to channel width in MHz
        // Higher bandwidth allocation gets wider channels

        double effectiveBandwidth = std::max(0.0, std::min(1.0, bandwidth));

        if (effectiveBandwidth >= 0.8) {
            return 80;      // 80 MHz channel
        } else if (effectiveBandwidth >= 0.6) {
            return 40;      // 40 MHz channel
        } else if (effectiveBandwidth >= 0.4) {
            return 20;      // 20 MHz channel
        } else {
            return 10;      // 10 MHz channel (minimum)
        }
    }

    double MultiTaskFLExperiment::ConfigureTransmissionPower(Ptr<NetDevice> device, double power) {
        // Map power allocation [0,1] to transmission power in dBm
        // Higher power allocation gets higher transmission power

        double effectivePower = std::max(0.0, std::min(1.0, power));

        // Power range: 5 dBm to 30 dBm
        double minPower = 5.0;
        double maxPower = 30.0;

        double txPower = minPower + (effectivePower * (maxPower - minPower));

        return txPower;
    }

    ApplicationContainer MultiTaskFLExperiment::CreateFileTransferApplication(Ptr<Node> clientNode,
                                                                             Ipv4Address serverAddress,
                                                                             const TaskExecution &taskExecution,
                                                                             uint16_t basePort) {

        ApplicationContainer allApps;

        // Create server application (file receiver)
        uint16_t serverPort = basePort;
        PacketSinkHelper sinkHelper("ns3::TcpSocketFactory",
                                   InetSocketAddress(Ipv4Address::GetAny(), serverPort));
        ApplicationContainer serverApp = sinkHelper.Install(m_nodes.Get(m_numClients)); // Server node

        // Server should be ready before WiFi transmission starts
        serverApp.Start(Seconds(0.0));
        serverApp.Stop(Seconds(taskExecution.computeTime + 10.0)); // Run longer than total time
        allApps.Add(serverApp);

        // Create client application (file sender)
        BulkSendHelper bulkSendHelper("ns3::TcpSocketFactory",
                                     InetSocketAddress(serverAddress, serverPort));

        // Calculate total data to send based on file size Fs_ij
        uint64_t totalBytes = static_cast<uint64_t>(taskExecution.fileSize * 1024 * 1024); // Convert MB to bytes
        bulkSendHelper.SetAttribute("MaxBytes", UintegerValue(totalBytes));

        // Set packet size based on bandwidth allocation
        uint32_t packetSize = std::max(512u,
            std::min((uint32_t)m_maxPacketSize, (uint32_t)(m_maxPacketSize * taskExecution.bandwidth)));
        bulkSendHelper.SetAttribute("SendSize", UintegerValue(packetSize));

        ApplicationContainer clientApp = bulkSendHelper.Install(clientNode);

        // IMPORTANT: WiFi transmission starts AFTER compute time T_ij
        // The timing will be set by the caller (RunSingleTaskSimulation)
        // Here we just create the application, timing is set externally

        allApps.Add(clientApp);

        NS_LOG_INFO("Created file transfer application:");
        NS_LOG_INFO("  File size: " << taskExecution.fileSize << " MB (" << totalBytes << " bytes)");
        NS_LOG_INFO("  Packet size: " << packetSize << " bytes");
        NS_LOG_INFO("  Compute time T_ij: " << taskExecution.computeTime << " seconds");
        NS_LOG_INFO("  WiFi transmission will start AFTER T_ij computation");
        NS_LOG_INFO("  Server port: " << serverPort);

        return allApps;
    }

    TaskResult MultiTaskFLExperiment::CollectTaskResults(const TaskExecution &taskExecution,
                                                        Ptr<FlowMonitor> monitor,
                                                        Ptr<Ipv4FlowClassifier> classifier,
                                                        Ipv4InterfaceContainer &interfaces,
                                                        double actualTxPower,
                                                        double actualBandwidth) {

        TaskResult result;
        result.clientId = taskExecution.clientId;
        result.taskId = taskExecution.taskId;
        result.taskName = taskExecution.taskName;
        result.power = actualTxPower;
        result.bandwidth = actualBandwidth;
        result.executionTime = taskExecution.computeTime;

        // Initialize default values
        result.packetLoss = 0.0;
        result.throughput = 0.0;
        result.latency = 0.0;

        // Collect flow monitor statistics
        monitor->CheckForLostPackets();
        std::map<FlowId, FlowMonitor::FlowStats> stats = monitor->GetFlowStats();

        // Find flows for this client
        Ipv4Address clientAddress = interfaces.GetAddress(taskExecution.clientId);

        double totalThroughput = 0.0;
        double totalLatency = 0.0;
        double totalPacketLoss = 0.0;
        int flowCount = 0;

        for (auto& flowPair : stats) {
            Ipv4FlowClassifier::FiveTuple t = classifier->FindFlow(flowPair.first);

            if (t.sourceAddress == clientAddress) {
                const FlowMonitor::FlowStats& flowStats = flowPair.second;

                if (flowStats.txPackets > 0) {
                    // Calculate throughput (Mbps)
                    double duration = flowStats.timeLastTxPacket.GetSeconds() - flowStats.timeFirstTxPacket.GetSeconds();
                    if (duration > 0) {
                        double throughput = (flowStats.txBytes * 8.0) / (duration * 1000000.0); // Mbps
                        totalThroughput += throughput;
                    }

                    // Calculate latency (ms) - average per-packet delay
                    if (flowStats.rxPackets > 0) {
                        double latency = flowStats.delaySum.GetMilliSeconds() / flowStats.rxPackets;
                        totalLatency += latency;
                    }

                    // Calculate packet loss ratio
                    double packetLoss = (double)(flowStats.txPackets - flowStats.rxPackets) / flowStats.txPackets;
                    totalPacketLoss += packetLoss;

                    flowCount++;

                    NS_LOG_DEBUG("Flow " << flowPair.first << ": "
                               << "TX=" << flowStats.txPackets << ", RX=" << flowStats.rxPackets
                               << ", Bytes=" << flowStats.txBytes);
                }
            }
        }

        // Average the metrics
        if (flowCount > 0) {
            result.throughput = totalThroughput / flowCount;
            result.latency = totalLatency / flowCount;
            result.packetLoss = totalPacketLoss / flowCount;
        }

        // Ensure packet loss is in valid range [0,1]
        result.packetLoss = std::max(0.0, std::min(1.0, result.packetLoss));

        NS_LOG_INFO("Task result collected:");
        NS_LOG_INFO("  Client " << result.clientId << " Task " << result.taskName);
        NS_LOG_INFO("  Bandwidth: " << result.bandwidth << " MHz");
        NS_LOG_INFO("  Power: " << result.power << " dBm");
        NS_LOG_INFO("  Throughput: " << result.throughput << " Mbps");
        NS_LOG_INFO("  Latency: " << result.latency << " ms");
        NS_LOG_INFO("  Packet Loss: " << result.packetLoss);
        NS_LOG_INFO("  Execution Time: " << result.executionTime << " s");

        return result;
    }
}
