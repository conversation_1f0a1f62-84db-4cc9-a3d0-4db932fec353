/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#ifndef MULTI_TASK_FL_EXPERIMENT_H
#define MULTI_TASK_FL_EXPERIMENT_H

#include "ns3/command-line.h"
#include "ns3/config.h"
#include "ns3/uinteger.h"
#include "ns3/string.h"
#include "ns3/log.h"
#include "ns3/yans-wifi-helper.h"
#include "ns3/mobility-helper.h"
#include "ns3/ipv4-address-helper.h"
#include "ns3/on-off-helper.h"
#include "ns3/yans-wifi-channel.h"
#include "ns3/mobility-model.h"
#include "ns3/packet-socket-helper.h"
#include "ns3/packet-socket-address.h"
#include "ns3/applications-module.h"
#include "ns3/wifi-module.h"
#include "ns3/internet-module.h"
#include "ns3/flow-monitor-module.h"
#include "ns3/traffic-control-module.h"
#include "ns3/csma-helper.h"

#include "multi-task-fl-socket-interface.h"

#include <memory>
#include <string>
#include <cstdio>

namespace ns3 {
    /**
     * \ingroup multi-task-fl-experiment
     * \brief Multi-task FL experiment implementation
     */
    class MultiTaskFLExperiment {
    public:
        /**
         * \brief Constructor
         * \param numClients      Number of clients
         * \param numTasks        Number of tasks
         * \param networkType     Network type (wifi or ethernet)
         * \param maxPacketSize   Max packet size
         * \param txGain          TX gain for wifi
         * \param modelSize       Model size
         * \param dataRate        Data rate
         * \param bAsync          Async mode flag
         * \param socketInterface Socket interface pointer
         * \param fp              File pointer for logging
         * \param round           Round number
         */
        MultiTaskFLExperiment(int numClients, int numTasks, std::string &networkType, 
                            int maxPacketSize, double txGain, double modelSize,
                            std::string &dataRate, bool bAsync, 
                            MultiTaskFLSocketInterface *socketInterface, FILE *fp, int round);

        /**
         * \brief Run multi-task experiment with sequential task execution
         * \param executionPlan   Task execution plan from Python
         * \param timeOffset      Time offset for continuous simulation
         * \return                Task simulation results
         */
        MultiTaskFLSocketInterface::SimulationResults
        RunMultiTaskExperiment(const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan, 
                             ns3::Time &timeOffset);

    private:
        /**
         * \brief Set position of node
         * \param node        Node to position
         * \param radius      Radius from center
         * \param theta       Angular position
         */
        void SetPosition(Ptr<Node> node, double radius, double theta);

        /**
         * \brief Get position of node
         * \param node   Node to get position of
         * \return       Position vector
         */
        Vector GetPosition(Ptr<Node> node);

        /**
         * \brief Setup WiFi network with dynamic configuration
         * \param nodes           Node container
         * \param executionPlan   Task execution plan
         * \return                Network device container
         */
        NetDeviceContainer SetupWifiNetwork(NodeContainer &nodes, 
                                           const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan);

        /**
         * \brief Setup Ethernet network
         * \param nodes           Node container
         * \param executionPlan   Task execution plan
         * \return                Network device container
         */
        NetDeviceContainer SetupEthernetNetwork(NodeContainer &nodes, 
                                              const MultiTaskFLSocketInterface::TaskExecutionPlan &executionPlan);

        /**
         * \brief Run single task simulation
         * \param nodes           Node container
         * \param interfaces      IP interfaces
         * \param taskExecution   Task execution info
         * \param monitor         Flow monitor
         * \param classifier      Flow classifier
         * \return                Task result
         */
        TaskResult RunSingleTaskSimulation(NodeContainer &nodes,
                                          Ipv4InterfaceContainer &interfaces,
                                          const TaskExecution &taskExecution,
                                          Ptr<FlowMonitor> monitor,
                                          Ptr<Ipv4FlowClassifier> classifier);

        /**
         * \brief Configure network for specific task
         * \param nodes           Node container
         * \param taskExecution   Task execution info
         */
        void ConfigureNetworkForTask(NodeContainer &nodes, const TaskExecution &taskExecution);

        /**
         * \brief Configure WiFi parameters for task
         * \param clientNode      Client node
         * \param taskExecution   Task execution info
         */
        void ConfigureWiFiForTask(Ptr<Node> clientNode, const TaskExecution &taskExecution);

        /**
         * \brief Configure Ethernet parameters for task
         * \param clientNode      Client node
         * \param taskExecution   Task execution info
         */
        void ConfigureEthernetForTask(Ptr<Node> clientNode, const TaskExecution &taskExecution);

        /**
         * \brief Configure channel width from bandwidth allocation
         * \param device          Network device
         * \param bandwidth       Bandwidth allocation [0,1]
         * \return                Channel width in MHz
         */
        uint16_t ConfigureChannelWidth(Ptr<NetDevice> device, double bandwidth);

        /**
         * \brief Configure transmission power from power allocation
         * \param device          Network device
         * \param power           Power allocation [0,1]
         * \return                TX power in dBm
         */
        double ConfigureTransmissionPower(Ptr<NetDevice> device, double power);

        /**
         * \brief Create file transfer application
         * \param clientNode      Client node
         * \param serverAddress   Server IP address
         * \param taskExecution   Task execution info
         * \param basePort        Base port number
         * \return                Application container
         */
        ApplicationContainer CreateFileTransferApplication(Ptr<Node> clientNode,
                                                          Ipv4Address serverAddress,
                                                          const TaskExecution &taskExecution,
                                                          uint16_t basePort);

        /**
         * \brief Collect task simulation results
         * \param taskExecution   Task execution info
         * \param monitor         Flow monitor
         * \param classifier      Flow classifier
         * \param interfaces      IP interfaces
         * \param actualTxPower   Actual transmission power used
         * \param actualBandwidth Actual bandwidth used
         * \return                Task result
         */
        TaskResult CollectTaskResults(const TaskExecution &taskExecution,
                                    Ptr<FlowMonitor> monitor,
                                    Ptr<Ipv4FlowClassifier> classifier,
                                    Ipv4InterfaceContainer &interfaces,
                                    double actualTxPower,
                                    double actualBandwidth);

        // Member variables
        int m_numClients;                                    //!< Number of clients
        int m_numTasks;                                     //!< Number of tasks
        std::string m_networkType;                          //!< Network type
        int m_maxPacketSize;                                //!< Max packet size
        double m_txGain;                                    //!< TX gain
        double m_modelSize;                                 //!< Model size
        std::string m_dataRate;                             //!< Data rate
        bool m_bAsync;                                      //!< Async mode
        MultiTaskFLSocketInterface *m_socketInterface;     //!< Socket interface
        FILE *m_fp;                                         //!< Log file
        int m_round;                                        //!< Round number

        // Network configuration
        double m_baseTxPower;                               //!< Base TX power (dBm)
        double m_maxBandwidth;                              //!< Max bandwidth (Mbps)
        
        // Simulation state
        NodeContainer m_nodes;                              //!< All nodes
        NetDeviceContainer m_devices;                       //!< All devices
        Ipv4InterfaceContainer m_interfaces;                //!< All interfaces
    };
}

#endif
