/* -*- Mode:C++; c-file-style:"gnu"; indent-tabs-mode:nil; -*- */
/*
 * Copyright (c) 2024 Multi-Task FL Team
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation;
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 *
 * Author: Multi-Task FL Team
 */

#include "multi-task-fl-socket-interface.h"
#include <algorithm>
#include <cmath>

namespace ns3 {

NS_LOG_COMPONENT_DEFINE ("MultiTaskFLSocketInterface");

// MultiTaskClientNode implementation
MultiTaskClientNode::MultiTaskClientNode (int clientId, double radius, double theta, int numTasks)
    : m_clientId (clientId), m_radius (radius), m_theta (theta), m_numTasks (numTasks)
{
  NS_LOG_INFO ("Created MultiTaskClientNode " << clientId << " at (" << radius << ", " << theta
                                              << ")");
}

// MultiTaskFLSocketInterface implementation
void
MultiTaskFLSocketInterface::WaitForConnection ()
{
  m_server_fd = socket (AF_INET, SOCK_STREAM, 0);
  if (m_server_fd < 0)
    {
      NS_LOG_UNCOND ("Could not create a socket");
      exit (-1);
    }
  int opt = 1;
  setsockopt (m_server_fd, SOL_SOCKET, SO_REUSEADDR | SO_REUSEPORT, &opt, sizeof (opt));

  m_address.sin_family = AF_INET;
  m_address.sin_addr.s_addr = INADDR_ANY;
  m_address.sin_port = htons (m_port);

  if (bind (m_server_fd, (struct sockaddr *) &m_address, sizeof (m_address)) == -1)
    {
      NS_LOG_UNCOND ("Could not bind to port " << m_port);
      exit (-1);
    }
  listen (m_server_fd, 3);

  NS_LOG_UNCOND ("Multi-Task FL Socket Interface waiting for connection on port " << m_port);

  int addrlen = sizeof (m_address);
  m_new_socket = accept (m_server_fd, (struct sockaddr *) &m_address, (socklen_t *) &addrlen);

  if (m_new_socket < 0)
    {
      NS_LOG_UNCOND ("Failed to accept connection");
      exit (-1);
    }

  NS_LOG_UNCOND ("Multi-Task FL Socket Interface connected successfully");
}

MultiTaskFLSocketInterface::TaskExecutionPlan
MultiTaskFLSocketInterface::ReceiveTaskExecutionPlan (
    std::map<int, std::shared_ptr<MultiTaskClientNode>> &clientNodes)
{

  TaskExecutionPlan plan;

  // Receive command header
  struct
  {
    uint32_t command;
    uint32_t numClients;
    uint32_t numTasks;
  } header;

  int len = read (m_new_socket, (char *) &header, sizeof (header));
  if (len != sizeof (header))
    {
      if (len != 0)
        {
          NS_LOG_UNCOND ("Invalid Command Header: Len(" << len << ")!=(" << sizeof (header) << ")");
        }
      else
        {
          NS_LOG_UNCOND ("Socket closed by Python");
        }
      plan.command = COMMAND::EXIT;
      return plan;
    }

  plan.command = static_cast<COMMAND> (header.command);
  plan.numClients = header.numClients;
  plan.numTasks = header.numTasks;

  if (plan.command == COMMAND::EXIT)
    {
      NS_LOG_UNCOND ("Exit command received");
      return plan;
    }
  else if (plan.command != COMMAND::RUN_SIMULATION)
    {
      NS_LOG_UNCOND ("Invalid command: " << static_cast<uint32_t> (plan.command));
      plan.command = COMMAND::EXIT;
      return plan;
    }

  NS_LOG_UNCOND ("Received RUN_SIMULATION command for " << plan.numClients << " clients, "
                                                        << plan.numTasks << " tasks");

  // Receive resource allocations for each client and convert to task executions
  std::vector<TaskExecution> allTaskExecutions;

  for (uint32_t i = 0; i < plan.numClients; i++)
    {
      uint32_t clientId;
      if (sizeof (clientId) != read (m_new_socket, (char *) &clientId, sizeof (clientId)))
        {
          NS_LOG_UNCOND ("Failed to read client ID");
          plan.command = COMMAND::EXIT;
          return plan;
        }

      // Receive resource allocation for this client
      std::vector<double> allocations = ReceiveResourceAllocation (clientId, plan.numTasks);
      if (allocations.empty ())
        {
          plan.command = COMMAND::EXIT;
          return plan;
        }

      // Convert to task executions
      std::vector<TaskExecution> clientTaskExecutions =
          ConvertToTaskExecutions (clientId, allocations, plan.numTasks);

      // Add to overall list
      allTaskExecutions.insert (allTaskExecutions.end (), clientTaskExecutions.begin (),
                                clientTaskExecutions.end ());

      NS_LOG_DEBUG ("Processed resource allocation for client " << clientId);
    }

  // Create execution timeline with proper ordering
  plan.taskExecutions = allTaskExecutions;
  // plan.taskExecutions = CreateExecutionTimeline (allTaskExecutions);

  NS_LOG_INFO ("Created execution plan with " << plan.taskExecutions.size () << " task executions");

  return plan;
}

std::vector<double>
MultiTaskFLSocketInterface::ReceiveResourceAllocation (uint32_t clientId, uint32_t numTasks)
{
  std::vector<double> allocations;

  // Each task has 6 allocation values: select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij
  uint32_t totalValues = numTasks * 6;
  allocations.resize (totalValues);

  for (uint32_t i = 0; i < totalValues; i++)
    {
      double value;
      if (sizeof (value) != read (m_new_socket, (char *) &value, sizeof (value)))
        {
          NS_LOG_UNCOND ("Failed to read allocation value " << i << " for client " << clientId);
          allocations.clear ();
          return allocations;
        }
      allocations[i] = value;
    }

  NS_LOG_DEBUG ("Received " << totalValues << " allocation values for client " << clientId);
  return allocations;
}

std::vector<TaskExecution>
MultiTaskFLSocketInterface::ConvertToTaskExecutions (uint32_t clientId,
                                                     const std::vector<double> &allocations,
                                                     uint32_t numTasks)
{

  std::vector<TaskExecution> taskExecutions;

  uint32_t idx = 0;
  for (uint32_t taskId = 0; taskId < numTasks; taskId++)
    {
      // Extract 6 values per task: select_ij, T_ij, B_ij, P_ij, Task_ij, Fs_ij
      double select_ij = allocations[idx++];
      double T_ij = allocations[idx++]; // Receive T_ij directly instead of q_ij
      double B_ij = allocations[idx++];
      double P_ij = allocations[idx++];
      double Task_ij = allocations[idx++];
      double Fs_ij = allocations[idx++]; // Receive Fs_ij directly from Python

      // Step 1: Convert select_ij to binary (>=0.5 -> 1, else 0)
      bool isSelected = select_ij >= 0.5;

      if (isSelected)
        {
          TaskExecution taskExec;
          taskExec.clientId = clientId;
          taskExec.taskId = taskId;
          taskExec.taskName = "task_" + std::to_string (taskId);
          taskExec.priority = Task_ij;
          taskExec.bandwidth = B_ij;
          taskExec.power = P_ij;
          taskExec.isSelected = true;

          // Use T_ij directly (already calculated in Python)
          taskExec.computeTime = T_ij;

          // Use Fs_ij directly (received from Python)
          taskExec.fileSize = Fs_ij;

          // Estimated Duration = T_ij (compute time) + estimated WiFi transmission time
          // This is ONLY for scheduling, NOT for forcing application stop
          // double estimatedTransmissionTime = EstimateTransmissionTime (Fs_ij, B_ij);
          // taskExec.estimatedDuration = taskExec.computeTime + estimatedTransmissionTime;
          // taskExec.actualDuration = 0.0; // Will be set after simulation completes

          taskExecutions.push_back (taskExec);

          NS_LOG_DEBUG ("Client " << clientId << " Task " << taskId << ": selected=" << isSelected
                                  << ", priority=" << Task_ij << ", compute_time="
                                  << taskExec.computeTime << "s (received T_ij)"
                                  << ", file_size=" << taskExec.fileSize << "MB (received Fs_ij)"
                                  << ", bandwidth=" << B_ij << ", power=" << P_ij);
        }
      else
        {
          NS_LOG_DEBUG ("Client " << clientId << " Task " << taskId
                                  << " not selected (select_ij=" << select_ij << ")");
        }
    }

  return taskExecutions;
}

// CalculateComputeTime function removed - T_ij is now calculated in Python and sent directly

// CalculateFileSize function removed - Fs_ij is now calculated in Python and sent directly

double
MultiTaskFLSocketInterface::EstimateTransmissionTime (double fileSize_MB,
                                                      double bandwidth_allocation)
{
  // Estimate WiFi transmission time (total time to send file) based on file size and bandwidth allocation
  // Note: This is different from latency (per-packet delay)

  // Map bandwidth allocation to approximate data rate (Mbps)
  double dataRate_Mbps = 1.0; // Default minimum

  if (bandwidth_allocation >= 0.8)
    {
      dataRate_Mbps = 40.0; // High bandwidth
    }
  else if (bandwidth_allocation >= 0.6)
    {
      dataRate_Mbps = 25.0; // Medium-high bandwidth
    }
  else if (bandwidth_allocation >= 0.4)
    {
      dataRate_Mbps = 15.0; // Medium bandwidth
    }
  else
    {
      dataRate_Mbps = 5.0; // Low bandwidth
    }

  // Convert file size to bits and calculate transmission time
  double fileSizeBits = fileSize_MB * 8 * 1024 * 1024; // MB to bits
  double transmissionTime = fileSizeBits / (dataRate_Mbps * 1000000); // seconds

  // Add protocol overhead (TCP, WiFi headers, etc.)
  transmissionTime *= 1.3; // 30% overhead

  // Clamp to reasonable bounds (0.5-10 seconds)
  transmissionTime = std::max (0.5, std::min (10.0, transmissionTime));

  return transmissionTime;
}

// std::vector<TaskExecution>
// MultiTaskFLSocketInterface::CreateExecutionTimeline (std::vector<TaskExecution> &taskExecutions)
// {

//   // Group tasks by client
//   std::map<int, std::vector<TaskExecution *>> clientTasks;
//   for (auto &taskExec : taskExecutions)
//     {
//       clientTasks[taskExec.clientId].push_back (&taskExec);
//     }

//   // Sort tasks within each client by priority (highest first)
//   for (auto &clientPair : clientTasks)
//     {
//       std::sort (clientPair.second.begin (), clientPair.second.end (),
//                  [] (const TaskExecution *a, const TaskExecution *b) {
//                    return a->priority > b->priority;
//                  });
//     }

//   // Assign start times for sequential execution within each client
//   double globalStartTime = 1.0; // Global start time

//   for (auto &clientPair : clientTasks)
//     {
//       int clientId = clientPair.first;
//       auto &tasks = clientPair.second;

//       double currentTime = globalStartTime;

//       NS_LOG_INFO ("Client " << clientId << " task execution order (by Task_ij priority):");
//       for (size_t i = 0; i < tasks.size (); i++)
//         {
//           TaskExecution *task = tasks[i];
//           task->startTime = currentTime + task->priority * 100;

//           double transmissionTime = task->estimatedDuration - task->computeTime;
//           double wifiStartTime = task->startTime + task->computeTime;

//           NS_LOG_INFO ("  " << (i + 1) << ". " << task->taskName << " (priority=" << task->priority
//                             << ")");
//           NS_LOG_INFO ("     Timeline: Start=" << task->startTime << "s"
//                                                << ", Compute=" << task->computeTime << "s (T_ij)"
//                                                << ", WiFi=" << transmissionTime << "s"
//                                                << ", Total=" << task->estimatedDuration << "s");
//           NS_LOG_INFO ("     WiFi transmission starts at " << wifiStartTime << "s");
//           NS_LOG_INFO ("     FileSize=" << task->fileSize << "MB"
//                                         << ", Bandwidth=" << task->bandwidth
//                                         << ", Power=" << task->power);

//           currentTime += task->estimatedDuration + 0.5; // Small gap between tasks
//         }
//     }

//   return taskExecutions;
// }

void
MultiTaskFLSocketInterface::SendResults (const SimulationResults &results)
{
  uint32_t numResults = results.taskResults.size ();

  // Send number of results
  if (write (m_new_socket, (char *) &numResults, sizeof (numResults)) != sizeof (numResults))
    {
      NS_LOG_UNCOND ("Failed to send number of results");
      return;
    }

  // Send each task result
  for (const auto &taskResult : results.taskResults)
    {
      // Send client ID and task ID (now both are uint32_t)
      if (write (m_new_socket, (char *) &taskResult.clientId, sizeof (taskResult.clientId)) !=
              sizeof (taskResult.clientId) ||
          write (m_new_socket, (char *) &taskResult.taskId, sizeof (taskResult.taskId)) !=
              sizeof (taskResult.taskId))
        {
          NS_LOG_UNCOND ("Failed to send client/task ID");
          return;
        }

      // Send task name
      uint32_t nameLen = taskResult.taskName.length ();
      if (write (m_new_socket, (char *) &nameLen, sizeof (nameLen)) != sizeof (nameLen) ||
          write (m_new_socket, taskResult.taskName.c_str (), nameLen) != (int) nameLen)
        {
          NS_LOG_UNCOND ("Failed to send task name");
          return;
        }

      // Send metrics: bandwidth, power, packetLoss, throughput, latency, executionTime
      NS_LOG_INFO ("Sending metrics for task "
                   << taskResult.taskName << ": bandwidth=" << taskResult.bandwidth
                   << ", power=" << taskResult.power << ", packetLoss=" << taskResult.packetLoss
                   << ", throughput=" << taskResult.throughput << ", latency=" << taskResult.latency
                   << ", executionTime=" << taskResult.executionTime);

      if (write (m_new_socket, (char *) &taskResult.bandwidth, sizeof (taskResult.bandwidth)) !=
              sizeof (taskResult.bandwidth) ||
          write (m_new_socket, (char *) &taskResult.power, sizeof (taskResult.power)) !=
              sizeof (taskResult.power) ||
          write (m_new_socket, (char *) &taskResult.packetLoss, sizeof (taskResult.packetLoss)) !=
              sizeof (taskResult.packetLoss) ||
          write (m_new_socket, (char *) &taskResult.throughput, sizeof (taskResult.throughput)) !=
              sizeof (taskResult.throughput) ||
          write (m_new_socket, (char *) &taskResult.latency, sizeof (taskResult.latency)) !=
              sizeof (taskResult.latency) ||
          write (m_new_socket, (char *) &taskResult.executionTime,
                 sizeof (taskResult.executionTime)) != sizeof (taskResult.executionTime))
        {
          NS_LOG_UNCOND ("Failed to send task metrics");
          return;
        }
    }

  NS_LOG_DEBUG ("Sent results for " << numResults << " task executions");
}

void
MultiTaskFLSocketInterface::Close ()
{
  if (m_new_socket >= 0)
    {
      close (m_new_socket);
    }
  if (m_server_fd >= 0)
    {
      close (m_server_fd);
    }
  NS_LOG_UNCOND ("Multi-Task FL Socket Interface connection closed");
}
} // namespace ns3
