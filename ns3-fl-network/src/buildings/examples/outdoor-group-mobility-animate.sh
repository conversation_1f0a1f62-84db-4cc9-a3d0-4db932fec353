#!/bin/bash

# Copyright (c) 2019, University of Padova, Dep. of Information Engineering, SIGNET lab
# Copyright (c) 2021, University of Washington: animation extensions
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License version 2 as
# published by the Free Software Foundation;
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
#
# Authors: <AUTHORS>
#          <PERSON> <<EMAIL>>

#
#  Plot the trace generated by outdoor-group-mobility-example.cc
#  in a manner that enables animation
#

# This Bash script is a variant of
# src/mobility/examples/reference-point-group-mobility-animate.sh that
# generates a large number of PNG image files, one for each node position
# traced in the mobility trace file 'outdoor-group-mobility-time-series.mob'
#
# This script relies on gnuplot (version 5.0 or greater).
#
# The PNGs are named in increasing numerical order from '0000.png' to
# '0799.png'.  The images can be assembled into an animated gif file
# using a tool such as ImageMagick's convert utility, such as:
#
# $ convert -delay 10 -loop 0 *.png outdoor-animation.gif
#
# and the output file 'outdoor-animation.gif' can be viewed by an image viewer. 
#
# Because this file generates many PNG files, it is recommended to move
# this script, the generated time-series mobility file named 
# 'outdoor-group-mobility-time-series.mob', and the generated buildings file
# 'outdoor-group-mobility-buildings.txt' to a subdirectory, and then
# run:
# $ ./outdoor-group-mobility-animation.sh
# $ convert -delay 10 -loop 0 *.png outdoor-animation.gif
#  

# The script first checks and enforces that only three nodes are present
num_nodes=`cat outdoor-group-mobility-time-series.mob | awk '{ print $2 }' | sort -n | uniq | wc -l`
if [ "$num_nodes" -ne "3" ]; then
    echo "Exiting: this tracing program designed only for 3 nodes"
    exit 1
fi
# reduce the trace
cat outdoor-group-mobility-time-series.mob | awk -F " " '{ print $3 }' | awk -F ":" '{ print $1" "$2 }' > ogm-time-series.tmp
# read three lines at a time from the reduced trace, convert to
# a plottable temporary file, and invoke gnuplot.  Loop until done.
n=0
while read p1 && read p2 && read p3; do
    basename=$(printf "%04d" "$n")
    cat >plotcmds <<EOL
# If you do not have pngcairo installed, you can 'set terminal png' below,
# but it will render the dashed bounding box as a solid line
set terminal pngcairo dashed
set output '$basename.png'
set view map
set xlabel 'X [m]'
set ylabel 'Y [m]'
set xrange [-10:110]
set yrange [-10:60]
set style fill transparent solid 0.5
unset key
set style fill  transparent solid 0.35 noborder
set style circle radius 0.5
# Define dashed lines to mark the outer bounding box
set arrow from 0,0 to 100,0 nohead dt "-" lc rgb "light-grey"
set arrow from 100,0 to 100,50 nohead dt "-" lc rgb "light-grey"
set arrow from 0,50 to 100,50 nohead dt "-" lc rgb "light-grey"
set arrow from 0,0 to 0,50 nohead dt "-" lc rgb "light-grey"
plot "<echo '$p1'" with circles lc rgb "red",\
     "<echo '$p2'" with circles lc rgb "blue",\
     "<echo '$p3'" with circles lc rgb "black"
EOL
    gnuplot outdoor-group-mobility-buildings.txt plotcmds
    rm plotcmds
    (( n++ ))
done < ogm-time-series.tmp
rm ogm-time-series.tmp
