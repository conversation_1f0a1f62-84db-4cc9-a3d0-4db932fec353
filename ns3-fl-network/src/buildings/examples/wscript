## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
    obj = bld.create_ns3_program('buildings-pathloss-profiler',
                                 ['buildings'])
    obj.source = 'buildings-pathloss-profiler.cc'
    obj = bld.create_ns3_program('outdoor-random-walk-example',
                                 ['buildings'])
    obj.source = 'outdoor-random-walk-example.cc'
    obj = bld.create_ns3_program('outdoor-group-mobility-example',
                                 ['mobility', 'network', 'buildings'])
    obj.source = 'outdoor-group-mobility-example.cc'
