## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):

    module = bld.create_ns3_module('antenna', ['core'])

    module.source = [
        'model/angles.cc',
        'model/antenna-model.cc',
        'model/isotropic-antenna-model.cc',
        'model/cosine-antenna-model.cc',
        'model/parabolic-antenna-model.cc',
        'model/three-gpp-antenna-model.cc',
        'model/phased-array-model.cc',
        'model/uniform-planar-array.cc',
        ]

    module_test = bld.create_ns3_module_test_library('antenna')
    module_test.source = [
        'test/test-angles.cc',
        'test/test-degrees-radians.cc',
        'test/test-isotropic-antenna.cc',
        'test/test-cosine-antenna.cc',
        'test/test-parabolic-antenna.cc',
        'test/test-uniform-planar-array.cc',
        ]

    # Tests encapsulating example programs should be listed here
    if (bld.env['ENABLE_EXAMPLES']):
        module_test.source.extend([
        #   'test/antenna-examples-test-suite.cc',
            ])
    
    headers = bld(features='ns3header')
    headers.module = 'antenna'
    headers.source = [
        'model/angles.h',
        'model/antenna-model.h',
        'model/isotropic-antenna-model.h',
        'model/cosine-antenna-model.h',
        'model/parabolic-antenna-model.h',
        'model/three-gpp-antenna-model.h',
        'model/phased-array-model.h',
        'model/uniform-planar-array.h',
        ]

    bld.ns3_python_bindings()
