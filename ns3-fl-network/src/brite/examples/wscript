## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):
   obj = bld.create_ns3_program('brite-generic-example', ['brite', 'internet', 'point-to-point', 'nix-vector-routing', 'applications'])
   obj.source = 'brite-generic-example.cc'
   obj = bld.create_ns3_program('brite-MPI-example', ['brite', 'internet', 'point-to-point', 'nix-vector-routing', 'applications', 'mpi'])
   obj.source = 'brite-MPI-example.cc'   
