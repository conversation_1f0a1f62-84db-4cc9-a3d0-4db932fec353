#This config file was generated by the GUI. 

BriteConfig



BeginModel
	Name = 5		 #Top Down = 5
	edgeConn = 2		 #Random=1, Smallest Nonleaf = 2, Smallest Deg = 3, k-Degree=4
	k = -1			 #Only needed if edgeConn is set to K-Degree, otherwise use -1
	BWInter = 1		 #Constant = 1, Uniform =2, HeavyTailed = 3, Exponential =4
	BWInterMin = 10.0
	BWInterMax = 1024.0
	BWIntra = 3		 #Constant = 1, Uniform =2, HeavyTailed = 3, Exponential =4
	BWIntraMin = 10.0
	BWIntraMax = 1024.0
EndModel

BeginModel
	Name =  4		 #Router Barabasi=2, AS Barabasi =4
	N = 2		 #Number of nodes in graph
	HS = 1000		 #Size of main plane (number of squares)
	LS = 100		 #Size of inner planes (number of squares)
	NodePlacement = 1	 #Random = 1, Heavy Tailed = 2
	m = 1			 #Number of neighboring node each new node connects to.
	BWDist = 1		 #Constant = 1, Uniform =2, HeavyTailed = 3, Exponential =4
	BWMin = -1.0
	BWMax = -1.0
EndModel

BeginModel
	Name =  1		 #Router Waxman=2, AS Waxman =3
	N = 5		 #Number of nodes in graph
	HS = 1000		 #Size of main plane (number of squares)
	LS = 100		 #Size of inner planes (number of squares)
	NodePlacement = 2	 #Random = 1, Heavy Tailed = 2
	GrowthType = 1		 #Incremental = 1, All = 2
	m = 1			 #Number of neighboring node each new node connects to.
	alpha = 0.5		 #Waxman Parameter
	beta = 0.8		 #Waxman Parameter
	BWDist = 1		 #Constant = 1, Uniform =2, HeavyTailed = 3, Exponential =4
	BWMin = -1.0
	BWMax = -1.0
EndModel

BeginOutput 			#**At least one of these options should have value 1**
	BRITE = 1		#0 = Do not save as BRITE, 1 = save as BRITE.  
	OTTER = 0		#0 = Do not visualize with Otter, 1 = Visualize
EndOutput

