## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

import os

from waflib import Options


def options(opt):
    opt.add_option('--with-brite',
                   help=('Use BRITE integration support, given by the indicated path,'
                         ' to allow the use of the BRITE topology generator'),
                   default=False, dest='with_brite')

def configure(conf):
    conf.env['ENABLE_BRITE'] = False

    lib_to_check = 'libbrite.so'
    if Options.options.with_brite:
        conf.msg("Checking BRITE location", ("%s (given)" % Options.options.with_brite))
        brite_dir = Options.options.with_brite
        if os.path.exists(os.path.join(brite_dir, lib_to_check)):
            conf.env['WITH_BRITE'] = os.path.abspath(Options.options.with_brite)
            conf.env['ENABLE_BRITE'] = True
        else:
            conf.report_optional_feature("brite", "BRITE Integration", False,
                                 "BRITE not found at requested location")
            # Add this module to the list of modules that won't be built
            # if they are enabled.
            conf.env['MODULES_NOT_BUILT'].append('brite')
            return
    else:
        # No user specified '--with-brite' option, try to guess
        # bake.py uses ../../build, while ns-3-dev uses ../BRITE
        brite_dir = os.path.join('..','BRITE')
        brite_bake_build_dir = os.path.join('..', '..', 'build') 
        brite_bake_lib_dir = os.path.join(brite_bake_build_dir, 'lib')
        if os.path.exists(os.path.join(brite_dir, lib_to_check)):
            conf.msg("Checking for BRITE location", ("%s (guessed)" % brite_dir))
            conf.env['WITH_BRITE'] = os.path.abspath(brite_dir)
            conf.env['ENABLE_BRITE'] = True
# Below is not yet ready (bake does not install BRITE yet, just builds it)
#        elif os.path.exists(os.path.join(brite_bake_lib_dir, lib_to_check)):
#            conf.msg("Checking for BRITE location", ("%s (guessed)" % brite_bake_lib_dir))
#            conf.env['WITH_BRITE'] = os.path.abspath(brite_bake_lib_dir)
        else:
            conf.report_optional_feature("brite", "BRITE Integration", False, 'BRITE not enabled (see option --with-brite)')

            # Add this module to the list of modules that won't be built
            # if they are enabled.
            conf.env['MODULES_NOT_BUILT'].append('brite')
            return

    test_code = '''
#include "Brite.h"

int main()
{
  return 0;
}
'''

    conf.env['HAVE_DL'] = conf.check(mandatory=True, lib='dl', define_name='HAVE_DL', uselib_store='DL')

    conf.env['LIBPATH_BRITE'] = [os.path.abspath(os.path.join(conf.env['WITH_BRITE'], '.'))]

    conf.env['INCLUDES_BRITE'] = [
            os.path.abspath(os.path.join(conf.env['WITH_BRITE'],'.')),
            os.path.abspath(os.path.join(conf.env['WITH_BRITE'],'Models'))
            ]

    conf.env['DEFINES_BRITE'] = ['NS3_BRITE']

    conf.env['BRITE'] = conf.check(fragment=test_code, lib='brite', libpath=conf.env['LIBPATH_BRITE'], uselib='BRITE')

    # This statement will get LD_LIBRARY_PATH set correctly when waf needs
    # to load the brite shared library.
    conf.env.append_value('NS3_MODULE_PATH',os.path.abspath(os.path.join(conf.env['WITH_BRITE'], '.')))

    conf.report_optional_feature("brite", "BRITE Integration",
                                          conf.env['BRITE'], "BRITE library not found")

def build(bld):
    # Don't do anything for this module if brite's not enabled.
    if 'brite' in bld.env['MODULES_NOT_BUILT']:
        return

    module = bld.create_ns3_module('brite', ['network', 'core', 'internet', 'point-to-point'])
    module.source = [
        ]

    module_test = bld.create_ns3_module_test_library('brite')
    module_test.source = [
        ]

    # Tests encapsulating example programs should be listed here
    if (bld.env['ENABLE_EXAMPLES']):
        module_test.source.extend([
        #   'test/brite-examples-test-suite.cc',
            ])
    
    if bld.env['BRITE'] and bld.env['HAVE_DL']:
        module.use.extend(['BRITE', 'DL'])
        module_test.use.extend(['BRITE', 'DL'])

    headers = bld(features='ns3header')
    headers.module = 'brite'
    headers.source = [
        ]

    if bld.env['ENABLE_BRITE']:
        module.source.append ('helper/brite-topology-helper.cc')
        headers.source.append ('helper/brite-topology-helper.h')
        module_test.source.append('test/brite-test-topology.cc')

    if bld.env['ENABLE_EXAMPLES'] and bld.env['ENABLE_BRITE']:
        bld.recurse('examples')
