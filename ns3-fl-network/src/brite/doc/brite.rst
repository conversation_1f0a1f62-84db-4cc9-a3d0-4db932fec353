.. include:: replace.txt
.. highlight:: bash

BRITE Integration
------------------

This model implements an interface to BRITE, the Boston university 
Representative Internet Topology gEnerator [1]_. BRITE is a standard tool for 
generating realistic internet topologies. The ns-3 model, described herein, 
provides a helper class to facilitate generating ns-3 specific topologies 
using BRITE configuration files. BRITE builds the original graph which is 
stored as nodes and edges in the ns-3 BriteTopolgyHelper class. In the ns-3
integration of BRITE, the generator generates a topology and then provides
access to leaf nodes for each AS generated.  ns-3 users can than attach 
custom topologies to these leaf nodes either by creating them manually or
using topology generators provided in ns-3.  

There are three major types of topologies available in BRITE:  Router, 
AS, and Hierarchical which is a combination of AS and Router.   For the 
purposes of ns-3 simulation, the most useful are likely to be Router and 
Hierarchical.  Router level topologies be generated using either the Waxman 
model or the Barabasi-Albert model.  Each model has different parameters that
effect topology creation.  For flat router topologies, all nodes are considered 
to be in the same AS.

BRITE Hierarchical topologies contain two levels.  The first is the AS level. 
This level can be also be created by using either the <PERSON><PERSON><PERSON> model or the 
Barabasi-<PERSON> model.  Then for each node in the AS topology, a router level 
topology is constructed.  These router level topologies can again either use 
the Waxman model or the Barbasi-Albert model.  BRITE interconnects these separate 
router topologies as specified by the AS level topology.  Once the hierarchical 
topology is constructed, it is flattened into a large router level topology.

Further information can be found in the BRITE user manual: http://www.cs.bu.edu/brite/publications/usermanual.pdf

Model Description
*****************

The model relies on building an external BRITE library, 
and then building some ns-3 helpers that call out to the library.  
The source code for the ns-3 helpers lives in the directory 
``src/brite/helper``.

Design
======

To generate the BRITE topology, ns-3 helpers call out to the external BRITE library, 
and using a standard BRITE configuration file, the BRITE code builds a graph with nodes 
and edges according to this configuration file. Please see the BRITE documentation
or the example configuration files in src/brite/examples/conf_files to get a better
grasp of BRITE configuration options. The graph built by BRITE is returned to ns-3, 
and a ns-3 implementation of the graph is built.  Leaf nodes for each AS are available
for the user to either attach custom topologies or install ns-3 applications directly.


References
==========

.. [1] Alberto Medina, Anukool Lakhina, Ibrahim Matta, and John Byers. BRITE: An Approach to Universal Topology Generation. In Proceedings of the International Workshop on Modeling, Analysis and Simulation of Computer and Telecommunications Systems- MASCOTS '01, Cincinnati, Ohio, August 2001.

Usage
*****

The brite-generic-example can be referenced to see basic usage of the BRITE
interface. In summary, the BriteTopologyHelper is used as the interface point
by passing in a BRITE configuration file. Along with the configuration file a
BRITE formatted random seed file can also be passed in.  If a seed file is not
passed in, the helper will create a seed file using ns-3's UniformRandomVariable.
Once the topology has been generated by BRITE, BuildBriteTopology() is called to
create the ns-3 representation.  Next IP Address can be assigned to the topology
using either AssignIpv4Addresses() or AssignIpv6Addresses().  It should be noted
that each point-to-point link in the topology will be treated as a new network 
therefore for IPV4 a /30 subnet should be used to avoid wasting a large amount of 
the available address space.  

Example BRITE configuration files can be found in /src/brite/examples/conf_files/.
ASBarbasi and ASWaxman are examples of AS only topologies.  The RTBarabasi and
RTWaxman files are examples of router only topologies.  Finally the 
TD_ASBarabasi_RTWaxman configuration file is an example of a Hierarchical topology
that uses the Barabasi-Albert model for the AS level and the Waxman model for each 
of the router level topologies.   Information on the BRITE parameters used in these files 
can be found in the BRITE user manual.


Building BRITE Integration
==========================

The first step is to download and build the ns-3 specific BRITE repository::

  $ hg clone http://code.nsnam.org/BRITE
  $ cd BRITE
  $ make

This will build BRITE and create a library, libbrite.so, within the BRITE 
directory.

Once BRITE has been built successfully, we proceed to configure ns-3 with 
BRITE support. Change to your ns-3 directory::

  $ ./waf configure --with-brite=/your/path/to/brite/source --enable-examples

Make sure it says 'enabled' beside 'BRITE Integration'. If it does not, then 
something has gone wrong. Either you have forgotten to build BRITE first 
following the steps above, or ns-3 could not find your BRITE directory.

Next, build ns-3::

  $ ./waf

Examples
========
For an example demonstrating BRITE integration
run::

  $ ./waf --run 'brite-generic-example'

By enabling the verbose parameter, the example will print out the node and 
edge information in a similar format to standard BRITE output. There are 
many other command-line parameters including confFile, tracing, and nix, described below:
   
  confFile
    A BRITE configuration file. Many different BRITE configuration 
    file examples exist in the src/brite/examples/conf_files directory, for 
    example, RTBarabasi20.conf and RTWaxman.conf. Please refer to 
    the conf_files directory for more examples.

  tracing
    Enables ascii tracing.

  nix
    Enables nix-vector routing. Global routing is used by default.

The generic BRITE example also support visualization using pyviz, assuming
python bindings in ns-3 are enabled::

  $ ./waf --run brite-generic-example --vis
  
Simulations involving BRITE can also be used with MPI.  The total number of MPI instances is 
passed to the BRITE topology helper where a modulo divide is used to assign the nodes for each 
AS to a MPI instance.  An example can be found in src/brite/examples::

  $ mpirun -np 2 ./waf --run brite-MPI-example
	
Please see the ns-3 MPI documentation for information on setting up MPI with ns-3.


