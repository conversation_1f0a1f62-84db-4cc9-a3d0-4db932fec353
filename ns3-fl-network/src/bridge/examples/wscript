## -*- Mode: python; py-indent-offset: 4; indent-tabs-mode: nil; coding: utf-8; -*-

def build(bld):

    obj = bld.create_ns3_program('csma-bridge', ['bridge', 'csma', 'internet', 'applications'])
    obj.source = 'csma-bridge.cc'

    bld.register_ns3_script('csma-bridge.py', ['bridge', 'csma', 'internet', 'applications'])

    obj = bld.create_ns3_program('csma-bridge-one-hop', ['bridge', 'csma', 'internet', 'applications'])
    obj.source = 'csma-bridge-one-hop.cc'

